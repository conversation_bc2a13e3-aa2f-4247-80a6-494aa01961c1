(()=>{var a={};a.id=974,a.ids=[974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(1658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},512:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{default:function(){return p},defaultHead:function(){return l}});let d=c(4985),e=c(740),f=c(687),g=e._(c(3210)),h=d._(c(7755)),i=c(4959),j=c(9513),k=c(4604);function l(a){void 0===a&&(a=!1);let b=[(0,f.jsx)("meta",{charSet:"utf-8"},"charset")];return a||b.push((0,f.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),b}function m(a,b){return"string"==typeof b||"number"==typeof b?a:b.type===g.default.Fragment?a.concat(g.default.Children.toArray(b.props.children).reduce((a,b)=>"string"==typeof b||"number"==typeof b?a:a.concat(b),[])):a.concat(b)}c(148);let n=["name","httpEquiv","charSet","itemProp"];function o(a,b){let{inAmpMode:c}=b;return a.reduce(m,[]).reverse().concat(l(c).reverse()).filter(function(){let a=new Set,b=new Set,c=new Set,d={};return e=>{let f=!0,g=!1;if(e.key&&"number"!=typeof e.key&&e.key.indexOf("$")>0){g=!0;let b=e.key.slice(e.key.indexOf("$")+1);a.has(b)?f=!1:a.add(b)}switch(e.type){case"title":case"base":b.has(e.type)?f=!1:b.add(e.type);break;case"meta":for(let a=0,b=n.length;a<b;a++){let b=n[a];if(e.props.hasOwnProperty(b))if("charSet"===b)c.has(b)?f=!1:c.add(b);else{let a=e.props[b],c=d[b]||new Set;("name"!==b||!g)&&c.has(a)?f=!1:(c.add(a),d[b]=c)}}}return f}}()).reverse().map((a,b)=>{let c=a.key||b;return g.default.cloneElement(a,{key:c})})}let p=function(a){let{children:b}=a,c=(0,g.useContext)(i.AmpStateContext),d=(0,g.useContext)(j.HeadManagerContext);return(0,f.jsx)(h.default,{reduceComponentsToState:o,headManager:d,inAmpMode:(0,k.isInAmpMode)(c),children:b})};("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},907:(a,b,c)=>{"use strict";var d=c(3210),e=c(9760),f="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},g=e.useSyncExternalStore,h=d.useRef,i=d.useEffect,j=d.useMemo,k=d.useDebugValue;b.useSyncExternalStoreWithSelector=function(a,b,c,d,e){var l=h(null);if(null===l.current){var m={hasValue:!1,value:null};l.current=m}else m=l.current;var n=g(a,(l=j(function(){function a(a){if(!i){if(i=!0,g=a,a=d(a),void 0!==e&&m.hasValue){var b=m.value;if(e(b,a))return h=b}return h=a}if(b=h,f(g,a))return b;var c=d(a);return void 0!==e&&e(b,c)?(g=a,b):(g=a,h=c)}var g,h,i=!1,j=void 0===c?null:c;return[function(){return a(b())},null===j?void 0:function(){return a(j())}]},[b,c,d,e]))[0],l[1]);return i(function(){m.hasValue=!0,m.value=n},[n]),k(n),n}},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},1135:()=>{},1204:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\BaseGraph\\\\basegraph\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\page.tsx","default")},1261:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{default:function(){return i},getImageProps:function(){return h}});let d=c(4985),e=c(4953),f=c(6533),g=d._(c(1933));function h(a){let{props:b}=(0,e.getImgProps)(a,{defaultLoader:g.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[a,c]of Object.entries(b))void 0===c&&delete b[a];return{props:b}}let i=f.Image},1480:(a,b)=>{"use strict";function c(a){let{widthInt:b,heightInt:c,blurWidth:d,blurHeight:e,blurDataURL:f,objectFit:g}=a,h=d?40*d:b,i=e?40*e:c,j=h&&i?"viewBox='0 0 "+h+" "+i+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+j+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(j?"none":"contain"===g?"xMidYMid":"cover"===g?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+f+"'/%3E%3C/svg%3E"}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"getImageBlurSvg",{enumerable:!0,get:function(){return c}})},1763:()=>{},1933:(a,b)=>{"use strict";function c(a){var b;let{config:c,src:d,width:e,quality:f}=a,g=f||(null==(b=c.qualities)?void 0:b.reduce((a,b)=>Math.abs(b-75)<Math.abs(a-75)?b:a))||75;return c.path+"?url="+encodeURIComponent(d)+"&w="+e+"&q="+g+(d.startsWith("/_next/static/media/"),"")}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return d}}),c.__next_img_default=!0;let d=c},2756:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{VALID_LOADERS:function(){return c},imageConfigDefault:function(){return d}});let c=["default","imgix","cloudinary","akamai","custom"],d={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3038:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"useMergedRef",{enumerable:!0,get:function(){return e}});let d=c(3210);function e(a,b){let c=(0,d.useRef)(null),e=(0,d.useRef)(null);return(0,d.useCallback)(d=>{if(null===d){let a=c.current;a&&(c.current=null,a());let b=e.current;b&&(e.current=null,b())}else a&&(c.current=f(a,d)),b&&(e.current=f(b,d))},[a,b])}function f(a,b){if("function"!=typeof a)return a.current=b,()=>{a.current=null};{let c=a(b);return"function"==typeof c?c:()=>a(null)}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3332:(a,b,c)=>{"use strict";var d=c(3210),e="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},f=d.useState,g=d.useEffect,h=d.useLayoutEffect,i=d.useDebugValue;function j(a){var b=a.getSnapshot;a=a.value;try{var c=b();return!e(a,c)}catch(a){return!0}}var k="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(a,b){return b()}:function(a,b){var c=b(),d=f({inst:{value:c,getSnapshot:b}}),e=d[0].inst,k=d[1];return h(function(){e.value=c,e.getSnapshot=b,j(e)&&k({inst:e})},[a,c,b]),g(function(){return j(e)&&k({inst:e}),a(function(){j(e)&&k({inst:e})})},[a]),i(c),c};b.useSyncExternalStore=void 0!==d.useSyncExternalStore?d.useSyncExternalStore:k},3873:a=>{"use strict";a.exports=require("path")},4224:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,1204)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,4431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},4431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(7413),e=c(2376),f=c.n(e),g=c(8726),h=c.n(g);c(1135);let i={title:"Create Next App",description:"Generated by create next app"};function j({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:a})})}},4574:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5227,23)),Promise.resolve().then(c.t.bind(c,6346,23)),Promise.resolve().then(c.t.bind(c,7924,23)),Promise.resolve().then(c.t.bind(c,99,23)),Promise.resolve().then(c.t.bind(c,8243,23)),Promise.resolve().then(c.t.bind(c,8827,23)),Promise.resolve().then(c.t.bind(c,2763,23)),Promise.resolve().then(c.t.bind(c,7173,23)),Promise.resolve().then(c.bind(c,5587))},4604:(a,b)=>{"use strict";function c(a){let{ampFirst:b=!1,hybrid:c=!1,hasQuery:d=!1}=void 0===a?{}:a;return b||c&&d}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"isInAmpMode",{enumerable:!0,get:function(){return c}})},4951:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>fI});var d,e,f,g,h,i,j,k,l,m=c(687),n=c(3210);function o(){for(var a,b,c=0,d="",e=arguments.length;c<e;c++)(a=arguments[c])&&(b=function a(b){var c,d,e="";if("string"==typeof b||"number"==typeof b)e+=b;else if("object"==typeof b)if(Array.isArray(b)){var f=b.length;for(c=0;c<f;c++)b[c]&&(d=a(b[c]))&&(e&&(e+=" "),e+=d)}else for(d in b)b[d]&&(e&&(e+=" "),e+=d);return e}(a))&&(d&&(d+=" "),d+=b);return d}let p=n.forwardRef(({className:a,variant:b="default",size:c="default",...d},e)=>(0,m.jsx)("button",{className:o("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-primary text-primary-foreground hover:bg-primary/90":"default"===b,"bg-destructive text-destructive-foreground hover:bg-destructive/90":"destructive"===b,"border border-input bg-background hover:bg-accent hover:text-accent-foreground":"outline"===b,"bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===b,"hover:bg-accent hover:text-accent-foreground":"ghost"===b,"text-primary underline-offset-4 hover:underline":"link"===b},{"h-10 px-4 py-2":"default"===c,"h-9 rounded-md px-3":"sm"===c,"h-11 rounded-md px-8":"lg"===c,"h-10 w-10":"icon"===c},a),ref:e,...d}));p.displayName="Button";let q=n.forwardRef(({className:a,type:b,...c},d)=>(0,m.jsx)("input",{type:b,className:o("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:d,...c}));q.displayName="Input";let r=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},s=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var t={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,n.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:d,className:e="",children:f,iconNode:g,...h},i)=>(0,n.createElement)("svg",{ref:i,...t,width:b,height:b,stroke:a,strokeWidth:d?24*Number(c)/Number(b):c,className:s("lucide",e),...!f&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(h)&&{"aria-hidden":"true"},...h},[...g.map(([a,b])=>(0,n.createElement)(a,b)),...Array.isArray(f)?f:[f]])),v=(a,b)=>{let c=(0,n.forwardRef)(({className:c,...d},e)=>(0,n.createElement)(u,{ref:e,iconNode:b,className:s(`lucide-${r(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,c),...d}));return c.displayName=r(a),c},w=v("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]),x=v("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);function y({onAnalysisStart:a,onAnalysisComplete:b}){let[c,d]=(0,n.useState)(""),[e,f]=(0,n.useState)(""),[g,h]=(0,n.useState)(!1),i=async()=>{if(!c.trim())return void f("L\xfctfen bir GitHub repository URL&apos;si girin");f(""),h(!0),a();try{let a=await fetch("/api/analyze",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:c})});if(!a.ok){let b=await a.json();throw Error(b.error||"Analiz sırasında bir hata oluştu")}let d=await a.json();b(d)}catch(a){f(a instanceof Error?a.message:"Analiz sırasında bir hata oluştu")}finally{h(!1)}};return(0,m.jsxs)("div",{className:"space-y-4",children:[(0,m.jsxs)("div",{className:"flex space-x-2",children:[(0,m.jsx)("div",{className:"flex-1",children:(0,m.jsx)(q,{type:"url",placeholder:"https://github.com/username/repository",value:c,onChange:a=>d(a.target.value),className:"w-full",disabled:g})}),(0,m.jsxs)(p,{onClick:i,disabled:g||!c.trim(),className:"flex items-center space-x-2",children:[(0,m.jsx)(w,{className:"w-4 h-4"}),(0,m.jsx)("span",{children:g?"Analiz Ediliyor...":"Analiz Et"})]})]}),e&&(0,m.jsxs)("div",{className:"flex items-center space-x-2 text-red-600 dark:text-red-400",children:[(0,m.jsx)(x,{className:"w-4 h-4"}),(0,m.jsx)("span",{className:"text-sm",children:e})]}),(0,m.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[(0,m.jsx)("p",{children:"\xd6rnek: https://github.com/facebook/react veya https://github.com/microsoft/vscode"}),(0,m.jsx)("p",{className:"mt-1",children:"Not: B\xfcy\xfck repository'ler i\xe7in analiz biraz zaman alabilir."})]})]})}function z(a){if("string"==typeof a||"number"==typeof a)return""+a;let b="";if(Array.isArray(a))for(let c=0,d;c<a.length;c++)""!==(d=z(a[c]))&&(b+=(b&&" ")+d);else for(let c in a)a[c]&&(b+=(b&&" ")+c);return b}var A={value:()=>{}};function B(){for(var a,b=0,c=arguments.length,d={};b<c;++b){if(!(a=arguments[b]+"")||a in d||/[\s.]/.test(a))throw Error("illegal type: "+a);d[a]=[]}return new C(d)}function C(a){this._=a}function D(a,b,c){for(var d=0,e=a.length;d<e;++d)if(a[d].name===b){a[d]=A,a=a.slice(0,d).concat(a.slice(d+1));break}return null!=c&&a.push({name:b,value:c}),a}function E(){}function F(a){return null==a?E:function(){return this.querySelector(a)}}function G(){return[]}function H(a){return null==a?G:function(){return this.querySelectorAll(a)}}function I(a){return function(){return this.matches(a)}}function J(a){return function(b){return b.matches(a)}}C.prototype=B.prototype={constructor:C,on:function(a,b){var c,d=this._,e=(a+"").trim().split(/^|\s+/).map(function(a){var b="",c=a.indexOf(".");if(c>=0&&(b=a.slice(c+1),a=a.slice(0,c)),a&&!d.hasOwnProperty(a))throw Error("unknown type: "+a);return{type:a,name:b}}),f=-1,g=e.length;if(arguments.length<2){for(;++f<g;)if((c=(a=e[f]).type)&&(c=function(a,b){for(var c,d=0,e=a.length;d<e;++d)if((c=a[d]).name===b)return c.value}(d[c],a.name)))return c;return}if(null!=b&&"function"!=typeof b)throw Error("invalid callback: "+b);for(;++f<g;)if(c=(a=e[f]).type)d[c]=D(d[c],a.name,b);else if(null==b)for(c in d)d[c]=D(d[c],a.name,null);return this},copy:function(){var a={},b=this._;for(var c in b)a[c]=b[c].slice();return new C(a)},call:function(a,b){if((c=arguments.length-2)>0)for(var c,d,e=Array(c),f=0;f<c;++f)e[f]=arguments[f+2];if(!this._.hasOwnProperty(a))throw Error("unknown type: "+a);for(d=this._[a],f=0,c=d.length;f<c;++f)d[f].value.apply(b,e)},apply:function(a,b,c){if(!this._.hasOwnProperty(a))throw Error("unknown type: "+a);for(var d=this._[a],e=0,f=d.length;e<f;++e)d[e].value.apply(b,c)}};var K=Array.prototype.find;function L(){return this.firstElementChild}var M=Array.prototype.filter;function N(){return Array.from(this.children)}function O(a){return Array(a.length)}function P(a,b){this.ownerDocument=a.ownerDocument,this.namespaceURI=a.namespaceURI,this._next=null,this._parent=a,this.__data__=b}function Q(a,b,c,d,e,f){for(var g,h=0,i=b.length,j=f.length;h<j;++h)(g=b[h])?(g.__data__=f[h],d[h]=g):c[h]=new P(a,f[h]);for(;h<i;++h)(g=b[h])&&(e[h]=g)}function R(a,b,c,d,e,f,g){var h,i,j,k=new Map,l=b.length,m=f.length,n=Array(l);for(h=0;h<l;++h)(i=b[h])&&(n[h]=j=g.call(i,i.__data__,h,b)+"",k.has(j)?e[h]=i:k.set(j,i));for(h=0;h<m;++h)j=g.call(a,f[h],h,f)+"",(i=k.get(j))?(d[h]=i,i.__data__=f[h],k.delete(j)):c[h]=new P(a,f[h]);for(h=0;h<l;++h)(i=b[h])&&k.get(n[h])===i&&(e[h]=i)}function S(a){return a.__data__}function T(a,b){return a<b?-1:a>b?1:a>=b?0:NaN}P.prototype={constructor:P,appendChild:function(a){return this._parent.insertBefore(a,this._next)},insertBefore:function(a,b){return this._parent.insertBefore(a,b)},querySelector:function(a){return this._parent.querySelector(a)},querySelectorAll:function(a){return this._parent.querySelectorAll(a)}};var U="http://www.w3.org/1999/xhtml";let V={svg:"http://www.w3.org/2000/svg",xhtml:U,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function W(a){var b=a+="",c=b.indexOf(":");return c>=0&&"xmlns"!==(b=a.slice(0,c))&&(a=a.slice(c+1)),V.hasOwnProperty(b)?{space:V[b],local:a}:a}function X(a){return a.ownerDocument&&a.ownerDocument.defaultView||a.document&&a||a.defaultView}function Y(a,b){return a.style.getPropertyValue(b)||X(a).getComputedStyle(a,null).getPropertyValue(b)}function Z(a){return a.trim().split(/^|\s+/)}function $(a){return a.classList||new _(a)}function _(a){this._node=a,this._names=Z(a.getAttribute("class")||"")}function aa(a,b){for(var c=$(a),d=-1,e=b.length;++d<e;)c.add(b[d])}function ab(a,b){for(var c=$(a),d=-1,e=b.length;++d<e;)c.remove(b[d])}function ac(){this.textContent=""}function ad(){this.innerHTML=""}function ae(){this.nextSibling&&this.parentNode.appendChild(this)}function af(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function ag(a){var b=W(a);return(b.local?function(a){return function(){return this.ownerDocument.createElementNS(a.space,a.local)}}:function(a){return function(){var b=this.ownerDocument,c=this.namespaceURI;return c===U&&b.documentElement.namespaceURI===U?b.createElement(a):b.createElementNS(c,a)}})(b)}function ah(){return null}function ai(){var a=this.parentNode;a&&a.removeChild(this)}function aj(){var a=this.cloneNode(!1),b=this.parentNode;return b?b.insertBefore(a,this.nextSibling):a}function ak(){var a=this.cloneNode(!0),b=this.parentNode;return b?b.insertBefore(a,this.nextSibling):a}function al(a){return function(){var b=this.__on;if(b){for(var c,d=0,e=-1,f=b.length;d<f;++d)(c=b[d],a.type&&c.type!==a.type||c.name!==a.name)?b[++e]=c:this.removeEventListener(c.type,c.listener,c.options);++e?b.length=e:delete this.__on}}}function am(a,b,c){return function(){var d,e=this.__on,f=function(a){b.call(this,a,this.__data__)};if(e){for(var g=0,h=e.length;g<h;++g)if((d=e[g]).type===a.type&&d.name===a.name){this.removeEventListener(d.type,d.listener,d.options),this.addEventListener(d.type,d.listener=f,d.options=c),d.value=b;return}}this.addEventListener(a.type,f,c),d={type:a.type,name:a.name,value:b,listener:f,options:c},e?e.push(d):this.__on=[d]}}function an(a,b,c){var d=X(a),e=d.CustomEvent;"function"==typeof e?e=new e(b,c):(e=d.document.createEvent("Event"),c?(e.initEvent(b,c.bubbles,c.cancelable),e.detail=c.detail):e.initEvent(b,!1,!1)),a.dispatchEvent(e)}_.prototype={add:function(a){0>this._names.indexOf(a)&&(this._names.push(a),this._node.setAttribute("class",this._names.join(" ")))},remove:function(a){var b=this._names.indexOf(a);b>=0&&(this._names.splice(b,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(a){return this._names.indexOf(a)>=0}};var ao=[null];function ap(a,b){this._groups=a,this._parents=b}function aq(){return new ap([[document.documentElement]],ao)}function ar(a){return((a=Math.exp(a))+1/a)/2}ap.prototype=aq.prototype={constructor:ap,select:function(a){"function"!=typeof a&&(a=F(a));for(var b=this._groups,c=b.length,d=Array(c),e=0;e<c;++e)for(var f,g,h=b[e],i=h.length,j=d[e]=Array(i),k=0;k<i;++k)(f=h[k])&&(g=a.call(f,f.__data__,k,h))&&("__data__"in f&&(g.__data__=f.__data__),j[k]=g);return new ap(d,this._parents)},selectAll:function(a){if("function"==typeof a){var b;b=a,a=function(){var a;return a=b.apply(this,arguments),null==a?[]:Array.isArray(a)?a:Array.from(a)}}else a=H(a);for(var c=this._groups,d=c.length,e=[],f=[],g=0;g<d;++g)for(var h,i=c[g],j=i.length,k=0;k<j;++k)(h=i[k])&&(e.push(a.call(h,h.__data__,k,i)),f.push(h));return new ap(e,f)},selectChild:function(a){var b;return this.select(null==a?L:(b="function"==typeof a?a:J(a),function(){return K.call(this.children,b)}))},selectChildren:function(a){var b;return this.selectAll(null==a?N:(b="function"==typeof a?a:J(a),function(){return M.call(this.children,b)}))},filter:function(a){"function"!=typeof a&&(a=I(a));for(var b=this._groups,c=b.length,d=Array(c),e=0;e<c;++e)for(var f,g=b[e],h=g.length,i=d[e]=[],j=0;j<h;++j)(f=g[j])&&a.call(f,f.__data__,j,g)&&i.push(f);return new ap(d,this._parents)},data:function(a,b){if(!arguments.length)return Array.from(this,S);var c=b?R:Q,d=this._parents,e=this._groups;"function"!=typeof a&&(s=a,a=function(){return s});for(var f=e.length,g=Array(f),h=Array(f),i=Array(f),j=0;j<f;++j){var k=d[j],l=e[j],m=l.length,n="object"==typeof(r=a.call(k,k&&k.__data__,j,d))&&"length"in r?r:Array.from(r),o=n.length,p=h[j]=Array(o),q=g[j]=Array(o);c(k,l,p,q,i[j]=Array(m),n,b);for(var r,s,t,u,v=0,w=0;v<o;++v)if(t=p[v]){for(v>=w&&(w=v+1);!(u=q[w])&&++w<o;);t._next=u||null}}return(g=new ap(g,d))._enter=h,g._exit=i,g},enter:function(){return new ap(this._enter||this._groups.map(O),this._parents)},exit:function(){return new ap(this._exit||this._groups.map(O),this._parents)},join:function(a,b,c){var d=this.enter(),e=this,f=this.exit();return"function"==typeof a?(d=a(d))&&(d=d.selection()):d=d.append(a+""),null!=b&&(e=b(e))&&(e=e.selection()),null==c?f.remove():c(f),d&&e?d.merge(e).order():e},merge:function(a){for(var b=a.selection?a.selection():a,c=this._groups,d=b._groups,e=c.length,f=d.length,g=Math.min(e,f),h=Array(e),i=0;i<g;++i)for(var j,k=c[i],l=d[i],m=k.length,n=h[i]=Array(m),o=0;o<m;++o)(j=k[o]||l[o])&&(n[o]=j);for(;i<e;++i)h[i]=c[i];return new ap(h,this._parents)},selection:function(){return this},order:function(){for(var a=this._groups,b=-1,c=a.length;++b<c;)for(var d,e=a[b],f=e.length-1,g=e[f];--f>=0;)(d=e[f])&&(g&&4^d.compareDocumentPosition(g)&&g.parentNode.insertBefore(d,g),g=d);return this},sort:function(a){function b(b,c){return b&&c?a(b.__data__,c.__data__):!b-!c}a||(a=T);for(var c=this._groups,d=c.length,e=Array(d),f=0;f<d;++f){for(var g,h=c[f],i=h.length,j=e[f]=Array(i),k=0;k<i;++k)(g=h[k])&&(j[k]=g);j.sort(b)}return new ap(e,this._parents).order()},call:function(){var a=arguments[0];return arguments[0]=this,a.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var a=this._groups,b=0,c=a.length;b<c;++b)for(var d=a[b],e=0,f=d.length;e<f;++e){var g=d[e];if(g)return g}return null},size:function(){let a=0;for(let b of this)++a;return a},empty:function(){return!this.node()},each:function(a){for(var b=this._groups,c=0,d=b.length;c<d;++c)for(var e,f=b[c],g=0,h=f.length;g<h;++g)(e=f[g])&&a.call(e,e.__data__,g,f);return this},attr:function(a,b){var c=W(a);if(arguments.length<2){var d=this.node();return c.local?d.getAttributeNS(c.space,c.local):d.getAttribute(c)}return this.each((null==b?c.local?function(a){return function(){this.removeAttributeNS(a.space,a.local)}}:function(a){return function(){this.removeAttribute(a)}}:"function"==typeof b?c.local?function(a,b){return function(){var c=b.apply(this,arguments);null==c?this.removeAttributeNS(a.space,a.local):this.setAttributeNS(a.space,a.local,c)}}:function(a,b){return function(){var c=b.apply(this,arguments);null==c?this.removeAttribute(a):this.setAttribute(a,c)}}:c.local?function(a,b){return function(){this.setAttributeNS(a.space,a.local,b)}}:function(a,b){return function(){this.setAttribute(a,b)}})(c,b))},style:function(a,b,c){return arguments.length>1?this.each((null==b?function(a){return function(){this.style.removeProperty(a)}}:"function"==typeof b?function(a,b,c){return function(){var d=b.apply(this,arguments);null==d?this.style.removeProperty(a):this.style.setProperty(a,d,c)}}:function(a,b,c){return function(){this.style.setProperty(a,b,c)}})(a,b,null==c?"":c)):Y(this.node(),a)},property:function(a,b){return arguments.length>1?this.each((null==b?function(a){return function(){delete this[a]}}:"function"==typeof b?function(a,b){return function(){var c=b.apply(this,arguments);null==c?delete this[a]:this[a]=c}}:function(a,b){return function(){this[a]=b}})(a,b)):this.node()[a]},classed:function(a,b){var c=Z(a+"");if(arguments.length<2){for(var d=$(this.node()),e=-1,f=c.length;++e<f;)if(!d.contains(c[e]))return!1;return!0}return this.each(("function"==typeof b?function(a,b){return function(){(b.apply(this,arguments)?aa:ab)(this,a)}}:b?function(a){return function(){aa(this,a)}}:function(a){return function(){ab(this,a)}})(c,b))},text:function(a){return arguments.length?this.each(null==a?ac:("function"==typeof a?function(a){return function(){var b=a.apply(this,arguments);this.textContent=null==b?"":b}}:function(a){return function(){this.textContent=a}})(a)):this.node().textContent},html:function(a){return arguments.length?this.each(null==a?ad:("function"==typeof a?function(a){return function(){var b=a.apply(this,arguments);this.innerHTML=null==b?"":b}}:function(a){return function(){this.innerHTML=a}})(a)):this.node().innerHTML},raise:function(){return this.each(ae)},lower:function(){return this.each(af)},append:function(a){var b="function"==typeof a?a:ag(a);return this.select(function(){return this.appendChild(b.apply(this,arguments))})},insert:function(a,b){var c="function"==typeof a?a:ag(a),d=null==b?ah:"function"==typeof b?b:F(b);return this.select(function(){return this.insertBefore(c.apply(this,arguments),d.apply(this,arguments)||null)})},remove:function(){return this.each(ai)},clone:function(a){return this.select(a?ak:aj)},datum:function(a){return arguments.length?this.property("__data__",a):this.node().__data__},on:function(a,b,c){var d,e,f=(a+"").trim().split(/^|\s+/).map(function(a){var b="",c=a.indexOf(".");return c>=0&&(b=a.slice(c+1),a=a.slice(0,c)),{type:a,name:b}}),g=f.length;if(arguments.length<2){var h=this.node().__on;if(h){for(var i,j=0,k=h.length;j<k;++j)for(d=0,i=h[j];d<g;++d)if((e=f[d]).type===i.type&&e.name===i.name)return i.value}return}for(d=0,h=b?am:al;d<g;++d)this.each(h(f[d],b,c));return this},dispatch:function(a,b){return this.each(("function"==typeof b?function(a,b){return function(){return an(this,a,b.apply(this,arguments))}}:function(a,b){return function(){return an(this,a,b)}})(a,b))},[Symbol.iterator]:function*(){for(var a=this._groups,b=0,c=a.length;b<c;++b)for(var d,e=a[b],f=0,g=e.length;f<g;++f)(d=e[f])&&(yield d)}},(function(a,{sourceEvent:b,subject:c,target:d,identifier:e,active:f,x:g,y:h,dx:i,dy:j,dispatch:k}){Object.defineProperties(this,{type:{value:a,enumerable:!0,configurable:!0},sourceEvent:{value:b,enumerable:!0,configurable:!0},subject:{value:c,enumerable:!0,configurable:!0},target:{value:d,enumerable:!0,configurable:!0},identifier:{value:e,enumerable:!0,configurable:!0},active:{value:f,enumerable:!0,configurable:!0},x:{value:g,enumerable:!0,configurable:!0},y:{value:h,enumerable:!0,configurable:!0},dx:{value:i,enumerable:!0,configurable:!0},dy:{value:j,enumerable:!0,configurable:!0},_:{value:k}})}).prototype.on=function(){var a=this._.on.apply(this._,arguments);return a===this._?this:a},!function a(b,c,d){function e(a,e){var f,g,h=a[0],i=a[1],j=a[2],k=e[0],l=e[1],m=e[2],n=k-h,o=l-i,p=n*n+o*o;if(p<1e-12)g=Math.log(m/j)/b,f=function(a){return[h+a*n,i+a*o,j*Math.exp(b*a*g)]};else{var q=Math.sqrt(p),r=(m*m-j*j+d*p)/(2*j*c*q),s=(m*m-j*j-d*p)/(2*m*c*q),t=Math.log(Math.sqrt(r*r+1)-r);g=(Math.log(Math.sqrt(s*s+1)-s)-t)/b,f=function(a){var d,e,f=a*g,k=ar(t),l=j/(c*q)*(k*(((d=Math.exp(2*(d=b*f+t)))-1)/(d+1))-((e=Math.exp(e=t))-1/e)/2);return[h+l*n,i+l*o,j*k/ar(b*f+t)]}}return f.duration=1e3*g*b/Math.SQRT2,f}return e.rho=function(b){var c=Math.max(.001,+b),d=c*c;return a(c,d,d*d)},e}(Math.SQRT2,2,4);var as,at,au=0,av=0,aw=0,ax=0,ay=0,az=0,aA="object"==typeof performance&&performance.now?performance:Date,aB="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(a){setTimeout(a,17)};function aC(){return ay||(aB(aD),ay=aA.now()+az)}function aD(){ay=0}function aE(){this._call=this._time=this._next=null}function aF(a,b,c){var d=new aE;return d.restart(a,b,c),d}function aG(){ay=(ax=aA.now())+az,au=av=0;try{aC(),++au;for(var a,b=as;b;)(a=ay-b._time)>=0&&b._call.call(void 0,a),b=b._next;--au}finally{au=0,function(){for(var a,b,c=as,d=1/0;c;)c._call?(d>c._time&&(d=c._time),a=c,c=c._next):(b=c._next,c._next=null,c=a?a._next=b:as=b);at=a,aI(d)}(),ay=0}}function aH(){var a=aA.now(),b=a-ax;b>1e3&&(az-=b,ax=a)}function aI(a){!au&&(av&&(av=clearTimeout(av)),a-ay>24?(a<1/0&&(av=setTimeout(aG,a-aA.now()-az)),aw&&(aw=clearInterval(aw))):(aw||(ax=aA.now(),aw=setInterval(aH,1e3)),au=1,aB(aG)))}function aJ(a,b,c){var d=new aE;return b=null==b?0:+b,d.restart(c=>{d.stop(),a(c+b)},b,c),d}aE.prototype=aF.prototype={constructor:aE,restart:function(a,b,c){if("function"!=typeof a)throw TypeError("callback is not a function");c=(null==c?aC():+c)+(null==b?0:+b),this._next||at===this||(at?at._next=this:as=this,at=this),this._call=a,this._time=c,aI()},stop:function(){this._call&&(this._call=null,this._time=1/0,aI())}};var aK=B("start","end","cancel","interrupt"),aL=[];function aM(a,b,c,d,e,f){var g=a.__transition;if(g){if(c in g)return}else a.__transition={};!function(a,b,c){var d,e=a.__transition;function f(i){var j,k,l,m;if(1!==c.state)return h();for(j in e)if((m=e[j]).name===c.name){if(3===m.state)return aJ(f);4===m.state?(m.state=6,m.timer.stop(),m.on.call("interrupt",a,a.__data__,m.index,m.group),delete e[j]):+j<b&&(m.state=6,m.timer.stop(),m.on.call("cancel",a,a.__data__,m.index,m.group),delete e[j])}if(aJ(function(){3===c.state&&(c.state=4,c.timer.restart(g,c.delay,c.time),g(i))}),c.state=2,c.on.call("start",a,a.__data__,c.index,c.group),2===c.state){for(j=0,c.state=3,d=Array(l=c.tween.length),k=-1;j<l;++j)(m=c.tween[j].value.call(a,a.__data__,c.index,c.group))&&(d[++k]=m);d.length=k+1}}function g(b){for(var e=b<c.duration?c.ease.call(null,b/c.duration):(c.timer.restart(h),c.state=5,1),f=-1,g=d.length;++f<g;)d[f].call(a,e);5===c.state&&(c.on.call("end",a,a.__data__,c.index,c.group),h())}function h(){for(var d in c.state=6,c.timer.stop(),delete e[b],e)return;delete a.__transition}e[b]=c,c.timer=aF(function(a){c.state=1,c.timer.restart(f,c.delay,c.time),c.delay<=a&&f(a-c.delay)},0,c.time)}(a,c,{name:b,index:d,group:e,on:aK,tween:aL,time:f.time,delay:f.delay,duration:f.duration,ease:f.ease,timer:null,state:0})}function aN(a,b){var c=aP(a,b);if(c.state>0)throw Error("too late; already scheduled");return c}function aO(a,b){var c=aP(a,b);if(c.state>3)throw Error("too late; already running");return c}function aP(a,b){var c=a.__transition;if(!c||!(c=c[b]))throw Error("transition not found");return c}function aQ(a,b){return a*=1,b*=1,function(c){return a*(1-c)+b*c}}var aR=180/Math.PI,aS={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function aT(a,b,c,d,e,f){var g,h,i;return(g=Math.sqrt(a*a+b*b))&&(a/=g,b/=g),(i=a*c+b*d)&&(c-=a*i,d-=b*i),(h=Math.sqrt(c*c+d*d))&&(c/=h,d/=h,i/=h),a*d<b*c&&(a=-a,b=-b,i=-i,g=-g),{translateX:e,translateY:f,rotate:Math.atan2(b,a)*aR,skewX:Math.atan(i)*aR,scaleX:g,scaleY:h}}function aU(a,b,c,d){function e(a){return a.length?a.pop()+" ":""}return function(f,g){var h,i,j,k,l=[],m=[];return f=a(f),g=a(g),!function(a,d,e,f,g,h){if(a!==e||d!==f){var i=g.push("translate(",null,b,null,c);h.push({i:i-4,x:aQ(a,e)},{i:i-2,x:aQ(d,f)})}else(e||f)&&g.push("translate("+e+b+f+c)}(f.translateX,f.translateY,g.translateX,g.translateY,l,m),h=f.rotate,i=g.rotate,h!==i?(h-i>180?i+=360:i-h>180&&(h+=360),m.push({i:l.push(e(l)+"rotate(",null,d)-2,x:aQ(h,i)})):i&&l.push(e(l)+"rotate("+i+d),j=f.skewX,k=g.skewX,j!==k?m.push({i:l.push(e(l)+"skewX(",null,d)-2,x:aQ(j,k)}):k&&l.push(e(l)+"skewX("+k+d),!function(a,b,c,d,f,g){if(a!==c||b!==d){var h=f.push(e(f)+"scale(",null,",",null,")");g.push({i:h-4,x:aQ(a,c)},{i:h-2,x:aQ(b,d)})}else(1!==c||1!==d)&&f.push(e(f)+"scale("+c+","+d+")")}(f.scaleX,f.scaleY,g.scaleX,g.scaleY,l,m),f=g=null,function(a){for(var b,c=-1,d=m.length;++c<d;)l[(b=m[c]).i]=b.x(a);return l.join("")}}}var aV=aU(function(a){let b=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(a+"");return b.isIdentity?aS:aT(b.a,b.b,b.c,b.d,b.e,b.f)},"px, ","px)","deg)"),aW=aU(function(a){return null==a?aS:(d||(d=document.createElementNS("http://www.w3.org/2000/svg","g")),d.setAttribute("transform",a),a=d.transform.baseVal.consolidate())?aT((a=a.matrix).a,a.b,a.c,a.d,a.e,a.f):aS},", ",")",")");function aX(a,b,c){var d=a._id;return a.each(function(){var a=aO(this,d);(a.value||(a.value={}))[b]=c.apply(this,arguments)}),function(a){return aP(a,d).value[b]}}function aY(a,b,c){a.prototype=b.prototype=c,c.constructor=a}function aZ(a,b){var c=Object.create(a.prototype);for(var d in b)c[d]=b[d];return c}function a$(){}var a_="\\s*([+-]?\\d+)\\s*",a0="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",a1="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",a2=/^#([0-9a-f]{3,8})$/,a3=RegExp(`^rgb\\(${a_},${a_},${a_}\\)$`),a4=RegExp(`^rgb\\(${a1},${a1},${a1}\\)$`),a5=RegExp(`^rgba\\(${a_},${a_},${a_},${a0}\\)$`),a6=RegExp(`^rgba\\(${a1},${a1},${a1},${a0}\\)$`),a7=RegExp(`^hsl\\(${a0},${a1},${a1}\\)$`),a8=RegExp(`^hsla\\(${a0},${a1},${a1},${a0}\\)$`),a9={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function ba(){return this.rgb().formatHex()}function bb(){return this.rgb().formatRgb()}function bc(a){var b,c;return a=(a+"").trim().toLowerCase(),(b=a2.exec(a))?(c=b[1].length,b=parseInt(b[1],16),6===c?bd(b):3===c?new bg(b>>8&15|b>>4&240,b>>4&15|240&b,(15&b)<<4|15&b,1):8===c?be(b>>24&255,b>>16&255,b>>8&255,(255&b)/255):4===c?be(b>>12&15|b>>8&240,b>>8&15|b>>4&240,b>>4&15|240&b,((15&b)<<4|15&b)/255):null):(b=a3.exec(a))?new bg(b[1],b[2],b[3],1):(b=a4.exec(a))?new bg(255*b[1]/100,255*b[2]/100,255*b[3]/100,1):(b=a5.exec(a))?be(b[1],b[2],b[3],b[4]):(b=a6.exec(a))?be(255*b[1]/100,255*b[2]/100,255*b[3]/100,b[4]):(b=a7.exec(a))?bm(b[1],b[2]/100,b[3]/100,1):(b=a8.exec(a))?bm(b[1],b[2]/100,b[3]/100,b[4]):a9.hasOwnProperty(a)?bd(a9[a]):"transparent"===a?new bg(NaN,NaN,NaN,0):null}function bd(a){return new bg(a>>16&255,a>>8&255,255&a,1)}function be(a,b,c,d){return d<=0&&(a=b=c=NaN),new bg(a,b,c,d)}function bf(a,b,c,d){var e;return 1==arguments.length?((e=a)instanceof a$||(e=bc(e)),e)?new bg((e=e.rgb()).r,e.g,e.b,e.opacity):new bg:new bg(a,b,c,null==d?1:d)}function bg(a,b,c,d){this.r=+a,this.g=+b,this.b=+c,this.opacity=+d}function bh(){return`#${bl(this.r)}${bl(this.g)}${bl(this.b)}`}function bi(){let a=bj(this.opacity);return`${1===a?"rgb(":"rgba("}${bk(this.r)}, ${bk(this.g)}, ${bk(this.b)}${1===a?")":`, ${a})`}`}function bj(a){return isNaN(a)?1:Math.max(0,Math.min(1,a))}function bk(a){return Math.max(0,Math.min(255,Math.round(a)||0))}function bl(a){return((a=bk(a))<16?"0":"")+a.toString(16)}function bm(a,b,c,d){return d<=0?a=b=c=NaN:c<=0||c>=1?a=b=NaN:b<=0&&(a=NaN),new bo(a,b,c,d)}function bn(a){if(a instanceof bo)return new bo(a.h,a.s,a.l,a.opacity);if(a instanceof a$||(a=bc(a)),!a)return new bo;if(a instanceof bo)return a;var b=(a=a.rgb()).r/255,c=a.g/255,d=a.b/255,e=Math.min(b,c,d),f=Math.max(b,c,d),g=NaN,h=f-e,i=(f+e)/2;return h?(g=b===f?(c-d)/h+(c<d)*6:c===f?(d-b)/h+2:(b-c)/h+4,h/=i<.5?f+e:2-f-e,g*=60):h=i>0&&i<1?0:g,new bo(g,h,i,a.opacity)}function bo(a,b,c,d){this.h=+a,this.s=+b,this.l=+c,this.opacity=+d}function bp(a){return(a=(a||0)%360)<0?a+360:a}function bq(a){return Math.max(0,Math.min(1,a||0))}function br(a,b,c){return(a<60?b+(c-b)*a/60:a<180?c:a<240?b+(c-b)*(240-a)/60:b)*255}function bs(a,b,c,d,e){var f=a*a,g=f*a;return((1-3*a+3*f-g)*b+(4-6*f+3*g)*c+(1+3*a+3*f-3*g)*d+g*e)/6}aY(a$,bc,{copy(a){return Object.assign(new this.constructor,this,a)},displayable(){return this.rgb().displayable()},hex:ba,formatHex:ba,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return bn(this).formatHsl()},formatRgb:bb,toString:bb}),aY(bg,bf,aZ(a$,{brighter(a){return a=null==a?1.4285714285714286:Math.pow(1.4285714285714286,a),new bg(this.r*a,this.g*a,this.b*a,this.opacity)},darker(a){return a=null==a?.7:Math.pow(.7,a),new bg(this.r*a,this.g*a,this.b*a,this.opacity)},rgb(){return this},clamp(){return new bg(bk(this.r),bk(this.g),bk(this.b),bj(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:bh,formatHex:bh,formatHex8:function(){return`#${bl(this.r)}${bl(this.g)}${bl(this.b)}${bl((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:bi,toString:bi})),aY(bo,function(a,b,c,d){return 1==arguments.length?bn(a):new bo(a,b,c,null==d?1:d)},aZ(a$,{brighter(a){return a=null==a?1.4285714285714286:Math.pow(1.4285714285714286,a),new bo(this.h,this.s,this.l*a,this.opacity)},darker(a){return a=null==a?.7:Math.pow(.7,a),new bo(this.h,this.s,this.l*a,this.opacity)},rgb(){var a=this.h%360+(this.h<0)*360,b=isNaN(a)||isNaN(this.s)?0:this.s,c=this.l,d=c+(c<.5?c:1-c)*b,e=2*c-d;return new bg(br(a>=240?a-240:a+120,e,d),br(a,e,d),br(a<120?a+240:a-120,e,d),this.opacity)},clamp(){return new bo(bp(this.h),bq(this.s),bq(this.l),bj(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let a=bj(this.opacity);return`${1===a?"hsl(":"hsla("}${bp(this.h)}, ${100*bq(this.s)}%, ${100*bq(this.l)}%${1===a?")":`, ${a})`}`}}));let bt=a=>()=>a;function bu(a,b){var c=b-a;return c?function(b){return a+b*c}:bt(isNaN(a)?b:a)}let bv=function a(b){var c,d=1==(c=+b)?bu:function(a,b){var d,e,f;return b-a?(d=a,e=b,d=Math.pow(d,f=c),e=Math.pow(e,f)-d,f=1/f,function(a){return Math.pow(d+a*e,f)}):bt(isNaN(a)?b:a)};function e(a,b){var c=d((a=bf(a)).r,(b=bf(b)).r),e=d(a.g,b.g),f=d(a.b,b.b),g=bu(a.opacity,b.opacity);return function(b){return a.r=c(b),a.g=e(b),a.b=f(b),a.opacity=g(b),a+""}}return e.gamma=a,e}(1);function bw(a){return function(b){var c,d,e=b.length,f=Array(e),g=Array(e),h=Array(e);for(c=0;c<e;++c)d=bf(b[c]),f[c]=d.r||0,g[c]=d.g||0,h[c]=d.b||0;return f=a(f),g=a(g),h=a(h),d.opacity=1,function(a){return d.r=f(a),d.g=g(a),d.b=h(a),d+""}}}bw(function(a){var b=a.length-1;return function(c){var d=c<=0?c=0:c>=1?(c=1,b-1):Math.floor(c*b),e=a[d],f=a[d+1],g=d>0?a[d-1]:2*e-f,h=d<b-1?a[d+2]:2*f-e;return bs((c-d/b)*b,g,e,f,h)}}),bw(function(a){var b=a.length;return function(c){var d=Math.floor(((c%=1)<0?++c:c)*b),e=a[(d+b-1)%b],f=a[d%b],g=a[(d+1)%b],h=a[(d+2)%b];return bs((c-d/b)*b,e,f,g,h)}});var bx=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,by=RegExp(bx.source,"g");function bz(a,b){var c,d,e,f,g,h=bx.lastIndex=by.lastIndex=0,i=-1,j=[],k=[];for(a+="",b+="";(e=bx.exec(a))&&(f=by.exec(b));)(g=f.index)>h&&(g=b.slice(h,g),j[i]?j[i]+=g:j[++i]=g),(e=e[0])===(f=f[0])?j[i]?j[i]+=f:j[++i]=f:(j[++i]=null,k.push({i:i,x:aQ(e,f)})),h=by.lastIndex;return h<b.length&&(g=b.slice(h),j[i]?j[i]+=g:j[++i]=g),j.length<2?k[0]?(c=k[0].x,function(a){return c(a)+""}):(d=b,function(){return d}):(b=k.length,function(a){for(var c,d=0;d<b;++d)j[(c=k[d]).i]=c.x(a);return j.join("")})}function bA(a,b){var c;return("number"==typeof b?aQ:b instanceof bc?bv:(c=bc(b))?(b=c,bv):bz)(a,b)}var bB=aq.prototype.constructor;function bC(a){return function(){this.style.removeProperty(a)}}var bD=0;function bE(a,b,c,d){this._groups=a,this._parents=b,this._name=c,this._id=d}var bF=aq.prototype;bE.prototype=(function(a){return aq().transition(a)}).prototype={constructor:bE,select:function(a){var b=this._name,c=this._id;"function"!=typeof a&&(a=F(a));for(var d=this._groups,e=d.length,f=Array(e),g=0;g<e;++g)for(var h,i,j=d[g],k=j.length,l=f[g]=Array(k),m=0;m<k;++m)(h=j[m])&&(i=a.call(h,h.__data__,m,j))&&("__data__"in h&&(i.__data__=h.__data__),l[m]=i,aM(l[m],b,c,m,l,aP(h,c)));return new bE(f,this._parents,b,c)},selectAll:function(a){var b=this._name,c=this._id;"function"!=typeof a&&(a=H(a));for(var d=this._groups,e=d.length,f=[],g=[],h=0;h<e;++h)for(var i,j=d[h],k=j.length,l=0;l<k;++l)if(i=j[l]){for(var m,n=a.call(i,i.__data__,l,j),o=aP(i,c),p=0,q=n.length;p<q;++p)(m=n[p])&&aM(m,b,c,p,n,o);f.push(n),g.push(i)}return new bE(f,g,b,c)},selectChild:bF.selectChild,selectChildren:bF.selectChildren,filter:function(a){"function"!=typeof a&&(a=I(a));for(var b=this._groups,c=b.length,d=Array(c),e=0;e<c;++e)for(var f,g=b[e],h=g.length,i=d[e]=[],j=0;j<h;++j)(f=g[j])&&a.call(f,f.__data__,j,g)&&i.push(f);return new bE(d,this._parents,this._name,this._id)},merge:function(a){if(a._id!==this._id)throw Error();for(var b=this._groups,c=a._groups,d=b.length,e=c.length,f=Math.min(d,e),g=Array(d),h=0;h<f;++h)for(var i,j=b[h],k=c[h],l=j.length,m=g[h]=Array(l),n=0;n<l;++n)(i=j[n]||k[n])&&(m[n]=i);for(;h<d;++h)g[h]=b[h];return new bE(g,this._parents,this._name,this._id)},selection:function(){return new bB(this._groups,this._parents)},transition:function(){for(var a=this._name,b=this._id,c=++bD,d=this._groups,e=d.length,f=0;f<e;++f)for(var g,h=d[f],i=h.length,j=0;j<i;++j)if(g=h[j]){var k=aP(g,b);aM(g,a,c,j,h,{time:k.time+k.delay+k.duration,delay:0,duration:k.duration,ease:k.ease})}return new bE(d,this._parents,a,c)},call:bF.call,nodes:bF.nodes,node:bF.node,size:bF.size,empty:bF.empty,each:bF.each,on:function(a,b){var c,d,e,f,g,h,i=this._id;return arguments.length<2?aP(this.node(),i).on.on(a):this.each((c=i,d=a,e=b,h=(d+"").trim().split(/^|\s+/).every(function(a){var b=a.indexOf(".");return b>=0&&(a=a.slice(0,b)),!a||"start"===a})?aN:aO,function(){var a=h(this,c),b=a.on;b!==f&&(g=(f=b).copy()).on(d,e),a.on=g}))},attr:function(a,b){var c=W(a),d="transform"===c?aW:bA;return this.attrTween(a,"function"==typeof b?(c.local?function(a,b,c){var d,e,f;return function(){var g,h,i=c(this);return null==i?void this.removeAttributeNS(a.space,a.local):(g=this.getAttributeNS(a.space,a.local))===(h=i+"")?null:g===d&&h===e?f:(e=h,f=b(d=g,i))}}:function(a,b,c){var d,e,f;return function(){var g,h,i=c(this);return null==i?void this.removeAttribute(a):(g=this.getAttribute(a))===(h=i+"")?null:g===d&&h===e?f:(e=h,f=b(d=g,i))}})(c,d,aX(this,"attr."+a,b)):null==b?(c.local?function(a){return function(){this.removeAttributeNS(a.space,a.local)}}:function(a){return function(){this.removeAttribute(a)}})(c):(c.local?function(a,b,c){var d,e,f=c+"";return function(){var g=this.getAttributeNS(a.space,a.local);return g===f?null:g===d?e:e=b(d=g,c)}}:function(a,b,c){var d,e,f=c+"";return function(){var g=this.getAttribute(a);return g===f?null:g===d?e:e=b(d=g,c)}})(c,d,b))},attrTween:function(a,b){var c="attr."+a;if(arguments.length<2)return(c=this.tween(c))&&c._value;if(null==b)return this.tween(c,null);if("function"!=typeof b)throw Error();var d=W(a);return this.tween(c,(d.local?function(a,b){var c,d;function e(){var e=b.apply(this,arguments);return e!==d&&(c=(d=e)&&function(b){this.setAttributeNS(a.space,a.local,e.call(this,b))}),c}return e._value=b,e}:function(a,b){var c,d;function e(){var e=b.apply(this,arguments);return e!==d&&(c=(d=e)&&function(b){this.setAttribute(a,e.call(this,b))}),c}return e._value=b,e})(d,b))},style:function(a,b,c){var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y="transform"==(a+="")?aV:bA;return null==b?this.styleTween(a,(d=a,function(){var a=Y(this,d),b=(this.style.removeProperty(d),Y(this,d));return a===b?null:a===e&&b===f?g:g=y(e=a,f=b)})).on("end.style."+a,bC(a)):"function"==typeof b?this.styleTween(a,(h=a,i=aX(this,"style."+a,b),function(){var a=Y(this,h),b=i(this),c=b+"";return null==b&&(this.style.removeProperty(h),c=b=Y(this,h)),a===c?null:a===j&&c===k?l:(k=c,l=y(j=a,b))})).each((m=this._id,t="end."+(s="style."+(n=a)),function(){var a=aO(this,m),b=a.on,c=null==a.value[s]?r||(r=bC(n)):void 0;(b!==o||q!==c)&&(p=(o=b).copy()).on(t,q=c),a.on=p})):this.styleTween(a,(u=a,x=b+"",function(){var a=Y(this,u);return a===x?null:a===v?w:w=y(v=a,b)}),c).on("end.style."+a,null)},styleTween:function(a,b,c){var d="style."+(a+="");if(arguments.length<2)return(d=this.tween(d))&&d._value;if(null==b)return this.tween(d,null);if("function"!=typeof b)throw Error();return this.tween(d,function(a,b,c){var d,e;function f(){var f=b.apply(this,arguments);return f!==e&&(d=(e=f)&&function(b){this.style.setProperty(a,f.call(this,b),c)}),d}return f._value=b,f}(a,b,null==c?"":c))},text:function(a){var b,c;return this.tween("text","function"==typeof a?(b=aX(this,"text",a),function(){var a=b(this);this.textContent=null==a?"":a}):(c=null==a?"":a+"",function(){this.textContent=c}))},textTween:function(a){var b="text";if(arguments.length<1)return(b=this.tween(b))&&b._value;if(null==a)return this.tween(b,null);if("function"!=typeof a)throw Error();return this.tween(b,function(a){var b,c;function d(){var d=a.apply(this,arguments);return d!==c&&(b=(c=d)&&function(a){this.textContent=d.call(this,a)}),b}return d._value=a,d}(a))},remove:function(){var a;return this.on("end.remove",(a=this._id,function(){var b=this.parentNode;for(var c in this.__transition)if(+c!==a)return;b&&b.removeChild(this)}))},tween:function(a,b){var c=this._id;if(a+="",arguments.length<2){for(var d,e=aP(this.node(),c).tween,f=0,g=e.length;f<g;++f)if((d=e[f]).name===a)return d.value;return null}return this.each((null==b?function(a,b){var c,d;return function(){var e=aO(this,a),f=e.tween;if(f!==c){d=c=f;for(var g=0,h=d.length;g<h;++g)if(d[g].name===b){(d=d.slice()).splice(g,1);break}}e.tween=d}}:function(a,b,c){var d,e;if("function"!=typeof c)throw Error();return function(){var f=aO(this,a),g=f.tween;if(g!==d){e=(d=g).slice();for(var h={name:b,value:c},i=0,j=e.length;i<j;++i)if(e[i].name===b){e[i]=h;break}i===j&&e.push(h)}f.tween=e}})(c,a,b))},delay:function(a){var b=this._id;return arguments.length?this.each(("function"==typeof a?function(a,b){return function(){aN(this,a).delay=+b.apply(this,arguments)}}:function(a,b){return b*=1,function(){aN(this,a).delay=b}})(b,a)):aP(this.node(),b).delay},duration:function(a){var b=this._id;return arguments.length?this.each(("function"==typeof a?function(a,b){return function(){aO(this,a).duration=+b.apply(this,arguments)}}:function(a,b){return b*=1,function(){aO(this,a).duration=b}})(b,a)):aP(this.node(),b).duration},ease:function(a){var b=this._id;return arguments.length?this.each(function(a,b){if("function"!=typeof b)throw Error();return function(){aO(this,a).ease=b}}(b,a)):aP(this.node(),b).ease},easeVarying:function(a){var b;if("function"!=typeof a)throw Error();return this.each((b=this._id,function(){var c=a.apply(this,arguments);if("function"!=typeof c)throw Error();aO(this,b).ease=c}))},end:function(){var a,b,c=this,d=c._id,e=c.size();return new Promise(function(f,g){var h={value:g},i={value:function(){0==--e&&f()}};c.each(function(){var c=aO(this,d),e=c.on;e!==a&&((b=(a=e).copy())._.cancel.push(h),b._.interrupt.push(h),b._.end.push(i)),c.on=b}),0===e&&f()})},[Symbol.iterator]:bF[Symbol.iterator]};var bG={time:null,delay:0,duration:250,ease:function(a){return((a*=2)<=1?a*a*a:(a-=2)*a*a+2)/2}};function bH(a,b,c){this.k=a,this.x=b,this.y=c}aq.prototype.interrupt=function(a){return this.each(function(){!function(a,b){var c,d,e,f=a.__transition,g=!0;if(f){for(e in b=null==b?null:b+"",f){if((c=f[e]).name!==b){g=!1;continue}d=c.state>2&&c.state<5,c.state=6,c.timer.stop(),c.on.call(d?"interrupt":"cancel",a,a.__data__,c.index,c.group),delete f[e]}g&&delete a.__transition}}(this,a)})},aq.prototype.transition=function(a){var b,c;a instanceof bE?(b=a._id,a=a._name):(b=++bD,(c=bG).time=aC(),a=null==a?null:a+"");for(var d=this._groups,e=d.length,f=0;f<e;++f)for(var g,h=d[f],i=h.length,j=0;j<i;++j)(g=h[j])&&aM(g,a,b,j,h,c||function(a,b){for(var c;!(c=a.__transition)||!(c=c[b]);)if(!(a=a.parentNode))throw Error(`transition ${b} not found`);return c}(g,b));return new bE(d,this._parents,a,b)},bH.prototype={constructor:bH,scale:function(a){return 1===a?this:new bH(this.k*a,this.x,this.y)},translate:function(a,b){return 0===a&0===b?this:new bH(this.k,this.x+this.k*a,this.y+this.k*b)},apply:function(a){return[a[0]*this.k+this.x,a[1]*this.k+this.y]},applyX:function(a){return a*this.k+this.x},applyY:function(a){return a*this.k+this.y},invert:function(a){return[(a[0]-this.x)/this.k,(a[1]-this.y)/this.k]},invertX:function(a){return(a-this.x)/this.k},invertY:function(a){return(a-this.y)/this.k},rescaleX:function(a){return a.copy().domain(a.range().map(this.invertX,this).map(a.invert,a))},rescaleY:function(a){return a.copy().domain(a.range().map(this.invertY,this).map(a.invert,a))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};new bH(1,0,0);bH.prototype;let bI={error001:()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001",error003:a=>`Node type "${a}" not found. Using fallback type "default".`,error005:()=>"Only child nodes can use a parent extent.",error006:()=>"Can't create edge. An edge needs a source and a target.",error009:a=>`Marker type "${a}" doesn't exist.`,error008:(a,{id:b,sourceHandle:c,targetHandle:d})=>`Couldn't create edge for ${a} handle id: "${"source"===a?c:d}", edge id: ${b}.`,error010:()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",error011:a=>`Edge type "${a}" not found. Using fallback type "default".`,error012:a=>`Node with id "${a}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`,error014:()=>"useNodeConnections: No node ID found. Call useNodeConnections inside a custom Node or provide a node ID.",error015:()=>"It seems that you are trying to drag a node that is not initialized. Please use onNodesChange as explained in the docs."},bJ=[[-1/0,-1/0],[1/0,1/0]],bK=["Enter"," ","Escape"],bL={"node.a11yDescription.default":"Press enter or space to select a node. Press delete to remove it and escape to cancel.","node.a11yDescription.keyboardDisabled":"Press enter or space to select a node. You can then use the arrow keys to move the node around. Press delete to remove it and escape to cancel.","node.a11yDescription.ariaLiveMessage":({direction:a,x:b,y:c})=>`Moved selected node ${a}. New position, x: ${b}, y: ${c}`,"edge.a11yDescription.default":"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel.","controls.ariaLabel":"Control Panel","controls.zoomIn.ariaLabel":"Zoom In","controls.zoomOut.ariaLabel":"Zoom Out","controls.fitView.ariaLabel":"Fit View","controls.interactive.ariaLabel":"Toggle Interactivity","minimap.ariaLabel":"Mini Map","handle.ariaLabel":"Handle"};!function(a){a.Strict="strict",a.Loose="loose"}(e||(e={})),function(a){a.Free="free",a.Vertical="vertical",a.Horizontal="horizontal"}(f||(f={})),function(a){a.Partial="partial",a.Full="full"}(g||(g={}));let bM={inProgress:!1,isValid:null,from:null,fromHandle:null,fromPosition:null,fromNode:null,to:null,toHandle:null,toPosition:null,toNode:null};!function(a){a.Bezier="default",a.Straight="straight",a.Step="step",a.SmoothStep="smoothstep",a.SimpleBezier="simplebezier"}(h||(h={})),function(a){a.Arrow="arrow",a.ArrowClosed="arrowclosed"}(i||(i={})),function(a){a.Left="left",a.Top="top",a.Right="right",a.Bottom="bottom"}(j||(j={}));let bN={[j.Left]:j.Right,[j.Right]:j.Left,[j.Top]:j.Bottom,[j.Bottom]:j.Top};function bO(a){return null===a?null:a?"valid":"invalid"}let bP=a=>"id"in a&&"source"in a&&"target"in a,bQ=a=>"id"in a&&"internals"in a&&!("source"in a)&&!("target"in a),bR=(a,b=[0,0])=>{let{width:c,height:d}=cf(a),e=a.origin??b,f=c*e[0],g=d*e[1];return{x:a.position.x-f,y:a.position.y-g}},bS=(a,b={})=>{if(0===a.size)return{x:0,y:0,width:0,height:0};let c={x:1/0,y:1/0,x2:-1/0,y2:-1/0};return a.forEach(a=>{if(void 0===b.filter||b.filter(a)){let b=b2(a);c=b$(c,b)}}),b0(c)},bT=(a,b,[c,d,e]=[0,0,1],f=!1,g=!1)=>{let h={...b9(b,[c,d,e]),width:b.width/e,height:b.height/e},i=[];for(let b of a.values()){let{measured:a,selectable:c=!0,hidden:d=!1}=b;if(g&&!c||d)continue;let e=a.width??b.width??b.initialWidth??null,j=a.height??b.height??b.initialHeight??null,k=b4(h,b1(b)),l=(e??0)*(j??0),m=f&&k>0;(!b.internals.handleBounds||m||k>=l||b.dragging)&&i.push(b)}return i};async function bU({nodes:a,width:b,height:c,panZoom:d,minZoom:e,maxZoom:f},g){if(0===a.size)return Promise.resolve(!0);let h=cc(bS(function(a,b){let c=new Map,d=b?.nodes?new Set(b.nodes.map(a=>a.id)):null;return a.forEach(a=>{a.measured.width&&a.measured.height&&(b?.includeHiddenNodes||!a.hidden)&&(!d||d.has(a.id))&&c.set(a.id,a)}),c}(a,g)),b,c,g?.minZoom??e,g?.maxZoom??f,g?.padding??.1);return await d.setViewport(h,{duration:g?.duration,ease:g?.ease,interpolate:g?.interpolate}),Promise.resolve(!0)}async function bV({nodesToRemove:a=[],edgesToRemove:b=[],nodes:c,edges:d,onBeforeDelete:e}){let f=new Set(a.map(a=>a.id)),g=[];for(let a of c){if(!1===a.deletable)continue;let b=f.has(a.id),c=!b&&a.parentId&&g.find(b=>b.id===a.parentId);(b||c)&&g.push(a)}let h=new Set(b.map(a=>a.id)),i=d.filter(a=>!1!==a.deletable),j=((a,b)=>{let c=new Set;return a.forEach(a=>{c.add(a.id)}),b.filter(a=>c.has(a.source)||c.has(a.target))})(g,i);for(let a of i)h.has(a.id)&&!j.find(b=>b.id===a.id)&&j.push(a);if(!e)return{edges:j,nodes:g};let k=await e({nodes:g,edges:j});return"boolean"==typeof k?k?{edges:j,nodes:g}:{edges:[],nodes:[]}:k}let bW=(a,b=0,c=1)=>Math.min(Math.max(a,b),c),bX=(a={x:0,y:0},b,c)=>({x:bW(a.x,b[0][0],b[1][0]-(c?.width??0)),y:bW(a.y,b[0][1],b[1][1]-(c?.height??0))});function bY(a,b,c){let{width:d,height:e}=cf(c),{x:f,y:g}=c.internals.positionAbsolute;return bX(a,[[f,g],[f+d,g+e]],b)}let bZ=(a,b,c)=>a<b?bW(Math.abs(a-b),1,b)/b:a>c?-bW(Math.abs(a-c),1,b)/b:0,b$=(a,b)=>({x:Math.min(a.x,b.x),y:Math.min(a.y,b.y),x2:Math.max(a.x2,b.x2),y2:Math.max(a.y2,b.y2)}),b_=({x:a,y:b,width:c,height:d})=>({x:a,y:b,x2:a+c,y2:b+d}),b0=({x:a,y:b,x2:c,y2:d})=>({x:a,y:b,width:c-a,height:d-b}),b1=(a,b=[0,0])=>{let{x:c,y:d}=bQ(a)?a.internals.positionAbsolute:bR(a,b);return{x:c,y:d,width:a.measured?.width??a.width??a.initialWidth??0,height:a.measured?.height??a.height??a.initialHeight??0}},b2=(a,b=[0,0])=>{let{x:c,y:d}=bQ(a)?a.internals.positionAbsolute:bR(a,b);return{x:c,y:d,x2:c+(a.measured?.width??a.width??a.initialWidth??0),y2:d+(a.measured?.height??a.height??a.initialHeight??0)}},b3=(a,b)=>b0(b$(b_(a),b_(b))),b4=(a,b)=>Math.ceil(Math.max(0,Math.min(a.x+a.width,b.x+b.width)-Math.max(a.x,b.x))*Math.max(0,Math.min(a.y+a.height,b.y+b.height)-Math.max(a.y,b.y))),b5=a=>b6(a.width)&&b6(a.height)&&b6(a.x)&&b6(a.y),b6=a=>!isNaN(a)&&isFinite(a),b7=(a,b)=>{},b8=(a,b=[1,1])=>({x:b[0]*Math.round(a.x/b[0]),y:b[1]*Math.round(a.y/b[1])}),b9=({x:a,y:b},[c,d,e],f=!1,g=[1,1])=>{let h={x:(a-c)/e,y:(b-d)/e};return f?b8(h,g):h},ca=({x:a,y:b},[c,d,e])=>({x:a*e+c,y:b*e+d});function cb(a,b){if("number"==typeof a)return Math.floor((b-b/(1+a))*.5);if("string"==typeof a&&a.endsWith("px")){let b=parseFloat(a);if(!Number.isNaN(b))return Math.floor(b)}if("string"==typeof a&&a.endsWith("%")){let c=parseFloat(a);if(!Number.isNaN(c))return Math.floor(b*c*.01)}return console.error(`[React Flow] The padding value "${a}" is invalid. Please provide a number or a string with a valid unit (px or %).`),0}let cc=(a,b,c,d,e,f)=>{let g=function(a,b,c){if("string"==typeof a||"number"==typeof a){let d=cb(a,c),e=cb(a,b);return{top:d,right:e,bottom:d,left:e,x:2*e,y:2*d}}if("object"==typeof a){let d=cb(a.top??a.y??0,c),e=cb(a.bottom??a.y??0,c),f=cb(a.left??a.x??0,b),g=cb(a.right??a.x??0,b);return{top:d,right:g,bottom:e,left:f,x:f+g,y:d+e}}return{top:0,right:0,bottom:0,left:0,x:0,y:0}}(f,b,c),h=bW(Math.min((b-g.x)/a.width,(c-g.y)/a.height),d,e),i=a.x+a.width/2,j=a.y+a.height/2,k=b/2-i*h,l=c/2-j*h,m=function(a,b,c,d,e,f){let{x:g,y:h}=ca(a,[b,c,d]),{x:i,y:j}=ca({x:a.x+a.width,y:a.y+a.height},[b,c,d]);return{left:Math.floor(g),top:Math.floor(h),right:Math.floor(e-i),bottom:Math.floor(f-j)}}(a,k,l,h,b,c),n={left:Math.min(m.left-g.left,0),top:Math.min(m.top-g.top,0),right:Math.min(m.right-g.right,0),bottom:Math.min(m.bottom-g.bottom,0)};return{x:k-n.left+n.right,y:l-n.top+n.bottom,zoom:h}},cd=()=>"undefined"!=typeof navigator&&navigator?.userAgent?.indexOf("Mac")>=0;function ce(a){return void 0!==a&&"parent"!==a}function cf(a){return{width:a.measured?.width??a.width??a.initialWidth??0,height:a.measured?.height??a.height??a.initialHeight??0}}function cg(a){return(a.measured?.width??a.width??a.initialWidth)!==void 0&&(a.measured?.height??a.height??a.initialHeight)!==void 0}function ch(a,b){if(a.size!==b.size)return!1;for(let c of a)if(!b.has(c))return!1;return!0}let ci=a=>({width:a.offsetWidth,height:a.offsetHeight}),cj=a=>a?.getRootNode?.()||window?.document,ck=["INPUT","SELECT","TEXTAREA"],cl=(a,b)=>{let c="clientX"in a,d=c?a.clientX:a.touches?.[0].clientX,e=c?a.clientY:a.touches?.[0].clientY;return{x:d-(b?.left??0),y:e-(b?.top??0)}},cm=(a,b,c,d,e)=>{let f=b.querySelectorAll(`.${a}`);return f&&f.length?Array.from(f).map(b=>{let f=b.getBoundingClientRect();return{id:b.getAttribute("data-handleid"),type:a,nodeId:e,position:b.getAttribute("data-handlepos"),x:(f.left-c.left)/d,y:(f.top-c.top)/d,...ci(b)}}):null};function cn({sourceX:a,sourceY:b,targetX:c,targetY:d,sourceControlX:e,sourceControlY:f,targetControlX:g,targetControlY:h}){let i=.125*a+.375*e+.375*g+.125*c,j=.125*b+.375*f+.375*h+.125*d,k=Math.abs(i-a),l=Math.abs(j-b);return[i,j,k,l]}function co(a,b){return a>=0?.5*a:25*b*Math.sqrt(-a)}function cp({pos:a,x1:b,y1:c,x2:d,y2:e,c:f}){switch(a){case j.Left:return[b-co(b-d,f),c];case j.Right:return[b+co(d-b,f),c];case j.Top:return[b,c-co(c-e,f)];case j.Bottom:return[b,c+co(e-c,f)]}}function cq({sourceX:a,sourceY:b,sourcePosition:c=j.Bottom,targetX:d,targetY:e,targetPosition:f=j.Top,curvature:g=.25}){let[h,i]=cp({pos:c,x1:a,y1:b,x2:d,y2:e,c:g}),[k,l]=cp({pos:f,x1:d,y1:e,x2:a,y2:b,c:g}),[m,n,o,p]=cn({sourceX:a,sourceY:b,targetX:d,targetY:e,sourceControlX:h,sourceControlY:i,targetControlX:k,targetControlY:l});return[`M${a},${b} C${h},${i} ${k},${l} ${d},${e}`,m,n,o,p]}function cr({sourceX:a,sourceY:b,targetX:c,targetY:d}){let e=Math.abs(c-a)/2,f=Math.abs(d-b)/2;return[c<a?c+e:c-e,d<b?d+f:d-f,e,f]}let cs=(a,b)=>{let c;return a.source&&a.target?((a,b)=>b.some(b=>b.source===a.source&&b.target===a.target&&(b.sourceHandle===a.sourceHandle||!b.sourceHandle&&!a.sourceHandle)&&(b.targetHandle===a.targetHandle||!b.targetHandle&&!a.targetHandle)))(c=bP(a)?{...a}:{...a,id:(({source:a,sourceHandle:b,target:c,targetHandle:d})=>`xy-edge__${a}${b||""}-${c}${d||""}`)(a)},b)?b:(null===c.sourceHandle&&delete c.sourceHandle,null===c.targetHandle&&delete c.targetHandle,b.concat(c)):(b7("006",bI.error006()),b)};function ct({sourceX:a,sourceY:b,targetX:c,targetY:d}){let[e,f,g,h]=cr({sourceX:a,sourceY:b,targetX:c,targetY:d});return[`M ${a},${b}L ${c},${d}`,e,f,g,h]}let cu={[j.Left]:{x:-1,y:0},[j.Right]:{x:1,y:0},[j.Top]:{x:0,y:-1},[j.Bottom]:{x:0,y:1}},cv=({source:a,sourcePosition:b=j.Bottom,target:c})=>b===j.Left||b===j.Right?a.x<c.x?{x:1,y:0}:{x:-1,y:0}:a.y<c.y?{x:0,y:1}:{x:0,y:-1},cw=(a,b)=>Math.sqrt(Math.pow(b.x-a.x,2)+Math.pow(b.y-a.y,2));function cx({sourceX:a,sourceY:b,sourcePosition:c=j.Bottom,targetX:d,targetY:e,targetPosition:f=j.Top,borderRadius:g=5,centerX:h,centerY:i,offset:k=20,stepPosition:l=.5}){let[m,n,o,p,q]=function({source:a,sourcePosition:b=j.Bottom,target:c,targetPosition:d=j.Top,center:e,offset:f,stepPosition:g}){let h,i,k=cu[b],l=cu[d],m={x:a.x+k.x*f,y:a.y+k.y*f},n={x:c.x+l.x*f,y:c.y+l.y*f},o=cv({source:m,sourcePosition:b,target:n}),p=0!==o.x?"x":"y",q=o[p],r=[],s={x:0,y:0},t={x:0,y:0},[,,u,v]=cr({sourceX:a.x,sourceY:a.y,targetX:c.x,targetY:c.y});if(k[p]*l[p]==-1){"x"===p?(h=e.x??m.x+(n.x-m.x)*g,i=e.y??(m.y+n.y)/2):(h=e.x??(m.x+n.x)/2,i=e.y??m.y+(n.y-m.y)*g);let a=[{x:h,y:m.y},{x:h,y:n.y}],b=[{x:m.x,y:i},{x:n.x,y:i}];r=k[p]===q?"x"===p?a:b:"x"===p?b:a}else{let e=[{x:m.x,y:n.y}],g=[{x:n.x,y:m.y}];if(r="x"===p?k.x===q?g:e:k.y===q?e:g,b===d){let b=Math.abs(a[p]-c[p]);if(b<=f){let d=Math.min(f-1,f-b);k[p]===q?s[p]=(m[p]>a[p]?-1:1)*d:t[p]=(n[p]>c[p]?-1:1)*d}}if(b!==d){let a="x"===p?"y":"x",b=k[p]===l[a],c=m[a]>n[a],d=m[a]<n[a];(1===k[p]&&(!b&&c||b&&d)||1!==k[p]&&(!b&&d||b&&c))&&(r="x"===p?e:g)}let j={x:m.x+s.x,y:m.y+s.y},o={x:n.x+t.x,y:n.y+t.y};Math.max(Math.abs(j.x-r[0].x),Math.abs(o.x-r[0].x))>=Math.max(Math.abs(j.y-r[0].y),Math.abs(o.y-r[0].y))?(h=(j.x+o.x)/2,i=r[0].y):(h=r[0].x,i=(j.y+o.y)/2)}return[[a,{x:m.x+s.x,y:m.y+s.y},...r,{x:n.x+t.x,y:n.y+t.y},c],h,i,u,v]}({source:{x:a,y:b},sourcePosition:c,target:{x:d,y:e},targetPosition:f,center:{x:h,y:i},offset:k,stepPosition:l});return[m.reduce((a,b,c)=>a+(c>0&&c<m.length-1?function(a,b,c,d){let e=Math.min(cw(a,b)/2,cw(b,c)/2,d),{x:f,y:g}=b;if(a.x===f&&f===c.x||a.y===g&&g===c.y)return`L${f} ${g}`;if(a.y===g){let b=a.x<c.x?-1:1,d=a.y<c.y?1:-1;return`L ${f+e*b},${g}Q ${f},${g} ${f},${g+e*d}`}let h=a.x<c.x?1:-1,i=a.y<c.y?-1:1;return`L ${f},${g+e*i}Q ${f},${g} ${f+e*h},${g}`}(m[c-1],b,m[c+1],g):`${0===c?"M":"L"}${b.x} ${b.y}`),""),n,o,p,q]}function cy(a){return a&&!!(a.internals.handleBounds||a.handles?.length)&&!!(a.measured.width||a.width||a.initialWidth)}function cz(a){if(!a)return null;let b=[],c=[];for(let d of a)d.width=d.width??1,d.height=d.height??1,"source"===d.type?b.push(d):"target"===d.type&&c.push(d);return{source:b,target:c}}function cA(a,b,c=j.Left,d=!1){let e=(b?.x??0)+a.internals.positionAbsolute.x,f=(b?.y??0)+a.internals.positionAbsolute.y,{width:g,height:h}=b??cf(a);if(d)return{x:e+g/2,y:f+h/2};switch(b?.position??c){case j.Top:return{x:e+g/2,y:f};case j.Right:return{x:e+g,y:f+h/2};case j.Bottom:return{x:e+g/2,y:f+h};case j.Left:return{x:e,y:f+h/2}}}function cB(a,b){return a&&(b?a.find(a=>a.id===b):a[0])||null}function cC(a,b){if(!a)return"";if("string"==typeof a)return a;let c=b?`${b}__`:"";return`${c}${Object.keys(a).sort().map(b=>`${b}=${a[b]}`).join("&")}`}let cD={nodeOrigin:[0,0],nodeExtent:bJ,elevateNodesOnSelect:!0,defaults:{}},cE={...cD,checkEquality:!0};function cF(a,b){let c={...a};for(let a in b)void 0!==b[a]&&(c[a]=b[a]);return c}function cG(a,b,c,d){let e=cF(cE,d),f=a.length>0,g=new Map(b),h=1e3*!!e?.elevateNodesOnSelect;for(let i of(b.clear(),c.clear(),a)){let a=g.get(i.id);if(e.checkEquality&&i===a?.internals.userNode)b.set(i.id,a);else{let c=bX(bR(i,e.nodeOrigin),ce(i.extent)?i.extent:e.nodeExtent,cf(i));a={...e.defaults,...i,measured:{width:i.measured?.width,height:i.measured?.height},internals:{positionAbsolute:c,handleBounds:i.measured?a?.internals.handleBounds:void 0,z:cI(i,h),userNode:i}},b.set(i.id,a)}void 0!==a.measured&&void 0!==a.measured.width&&void 0!==a.measured.height||a.hidden||(f=!1),i.parentId&&cH(a,b,c,d)}return f}function cH(a,b,c,d){let{elevateNodesOnSelect:e,nodeOrigin:f,nodeExtent:g}=cF(cD,d),h=a.parentId,i=b.get(h);if(!i)return void console.warn(`Parent node ${h} not found. Please make sure that parent nodes are in front of their child nodes in the nodes array.`);!function(a,b){if(!a.parentId)return;let c=b.get(a.parentId);c?c.set(a.id,a):b.set(a.parentId,new Map([[a.id,a]]))}(a,c);let{x:j,y:k,z:l}=function(a,b,c,d,e){let{x:f,y:g}=b.internals.positionAbsolute,h=cf(a),i=bR(a,c),j=ce(a.extent)?bX(i,a.extent,h):i,k=bX({x:f+j.x,y:g+j.y},d,h);"parent"===a.extent&&(k=bY(k,h,b));let l=cI(a,e),m=b.internals.z??0;return{x:k.x,y:k.y,z:m>=l?m+1:l}}(a,i,f,g,1e3*!!e),{positionAbsolute:m}=a.internals,n=j!==m.x||k!==m.y;(n||l!==a.internals.z)&&b.set(a.id,{...a,internals:{...a.internals,positionAbsolute:n?{x:j,y:k}:m,z:l}})}function cI(a,b){return(b6(a.zIndex)?a.zIndex:0)+(a.selected?b:0)}function cJ(a,b,c,d=[0,0]){let e=[],f=new Map;for(let c of a){let a=b.get(c.parentId);if(!a)continue;let d=b3(f.get(c.parentId)?.expandedRect??b1(a),c.rect);f.set(c.parentId,{expandedRect:d,parent:a})}return f.size>0&&f.forEach(({expandedRect:b,parent:f},g)=>{let h=f.internals.positionAbsolute,i=cf(f),j=f.origin??d,k=b.x<h.x?Math.round(Math.abs(h.x-b.x)):0,l=b.y<h.y?Math.round(Math.abs(h.y-b.y)):0,m=Math.max(i.width,Math.round(b.width)),n=Math.max(i.height,Math.round(b.height)),o=(m-i.width)*j[0],p=(n-i.height)*j[1];(k>0||l>0||o||p)&&(e.push({id:g,type:"position",position:{x:f.position.x-k+o,y:f.position.y-l+p}}),c.get(g)?.forEach(b=>{a.some(a=>a.id===b.id)||e.push({id:b.id,type:"position",position:{x:b.position.x+k,y:b.position.y+l}})})),(i.width<b.width||i.height<b.height||k||l)&&e.push({id:g,type:"dimensions",setAttributes:!0,dimensions:{width:m+(k?j[0]*k-o:0),height:n+(l?j[1]*l-p:0)}})}),e}async function cK({delta:a,panZoom:b,transform:c,translateExtent:d,width:e,height:f}){if(!b||!a.x&&!a.y)return Promise.resolve(!1);let g=await b.setViewportConstrained({x:c[0]+a.x,y:c[1]+a.y,zoom:c[2]},[[0,0],[e,f]],d);return Promise.resolve(!!g&&(g.x!==c[0]||g.y!==c[1]||g.k!==c[2]))}function cL(a,b,c,d,e,f){let g=e,h=d.get(g)||new Map;d.set(g,h.set(c,b)),g=`${e}-${a}`;let i=d.get(g)||new Map;if(d.set(g,i.set(c,b)),f){g=`${e}-${a}-${f}`;let h=d.get(g)||new Map;d.set(g,h.set(c,b))}}function cM(a,b,c){for(let d of(a.clear(),b.clear(),c)){let{source:c,target:e,sourceHandle:f=null,targetHandle:g=null}=d,h={edgeId:d.id,source:c,target:e,sourceHandle:f,targetHandle:g},i=`${c}-${f}--${e}-${g}`;cL("source",h,`${e}-${g}--${c}-${f}`,a,c,f),cL("target",h,i,a,e,g),b.set(d.id,d)}}function cN(a,b,c,d,e,f=!1){let g=d.get(a);if(!g)return null;let h="strict"===e?g.internals.handleBounds?.[b]:[...g.internals.handleBounds?.source??[],...g.internals.handleBounds?.target??[]],i=(c?h?.find(a=>a.id===c):h?.[0])??null;return i&&f?{...i,...cA(g,i,i.position,!0)}:i}function cO(a,b){return a?a:b?.classList.contains("target")?"target":b?.classList.contains("source")?"source":null}let cP=()=>!0;function cQ(a,{handle:b,connectionMode:c,fromNodeId:d,fromHandleId:f,fromType:g,doc:h,lib:i,flowId:j,isValidConnection:k=cP,nodeLookup:l}){let m="target"===g,n=b?h.querySelector(`.${i}-flow__handle[data-id="${j}-${b?.nodeId}-${b?.id}-${b?.type}"]`):null,{x:o,y:p}=cl(a),q=h.elementFromPoint(o,p),r=q?.classList.contains(`${i}-flow__handle`)?q:n,s={handleDomNode:r,isValid:!1,connection:null,toHandle:null};if(r){let a=cO(void 0,r),b=r.getAttribute("data-nodeid"),g=r.getAttribute("data-handleid"),h=r.classList.contains("connectable"),i=r.classList.contains("connectableend");if(!b||!a)return s;let j={source:m?b:d,sourceHandle:m?g:f,target:m?d:b,targetHandle:m?f:g};s.connection=j,s.isValid=h&&i&&(c===e.Strict?m&&"source"===a||!m&&"target"===a:b!==d||g!==f)&&k(j),s.toHandle=cN(b,a,g,l,c,!0)}return s}let cR={onPointerDown:function(a,{connectionMode:b,connectionRadius:c,handleId:d,nodeId:e,edgeUpdaterType:f,isTarget:g,domNode:h,nodeLookup:i,lib:k,autoPanOnConnect:l,flowId:m,panBy:n,cancelConnection:o,onConnectStart:p,onConnect:q,onConnectEnd:r,isValidConnection:s=cP,onReconnectEnd:t,updateConnection:u,getTransform:v,getFromHandle:w,autoPanSpeed:x,dragThreshold:y=1}){let z,A=cj(a.target),B=0,{x:C,y:D}=cl(a),E=cO(f,A?.elementFromPoint(C,D)),F=h?.getBoundingClientRect(),G=!1;if(!F||!E)return;let H=cN(e,E,d,i,b);if(!H)return;let I=cl(a,F),J=!1,K=null,L=!1,M=null,N={...H,nodeId:e,type:E,position:H.position},O=i.get(e),P={inProgress:!0,isValid:null,from:cA(O,N,j.Left,!0),fromHandle:N,fromPosition:N.position,fromNode:O,to:I,toHandle:null,toPosition:bN[N.position],toNode:null};function Q(){G=!0,u(P),p?.(a,{nodeId:e,handleId:d,handleType:E})}function R(a){var f,h;let j;if(!G){let{x:b,y:c}=cl(a),d=b-C,e=c-D;if(!(d*d+e*e>y*y))return;Q()}if(!w()||!N)return void S(a);let o=v();z=function(a,b,c,d){let e=[],f=1/0;for(let g of function(a,b,c){let d=[],e={x:a.x-c,y:a.y-c,width:2*c,height:2*c};for(let a of b.values())b4(e,b1(a))>0&&d.push(a);return d}(a,c,b+250))for(let c of[...g.internals.handleBounds?.source??[],...g.internals.handleBounds?.target??[]]){if(d.nodeId===c.nodeId&&d.type===c.type&&d.id===c.id)continue;let{x:h,y:i}=cA(g,c,c.position,!0),j=Math.sqrt(Math.pow(h-a.x,2)+Math.pow(i-a.y,2));j>b||(j<f?(e=[{...c,x:h,y:i}],f=j):j===f&&e.push({...c,x:h,y:i}))}if(!e.length)return null;if(e.length>1){let a="source"===d.type?"target":"source";return e.find(b=>b.type===a)??e[0]}return e[0]}(b9(I=cl(a,F),o,!1,[1,1]),c,i,N),J||(!function a(){if(!l||!F)return;let[b,c]=((a,b,c=15,d=40)=>[bZ(a.x,d,b.width-d)*c,bZ(a.y,d,b.height-d)*c])(I,F,x);n({x:b,y:c}),B=requestAnimationFrame(a)}(),J=!0);let p=cQ(a,{handle:z,connectionMode:b,fromNodeId:e,fromHandleId:d,fromType:g?"target":"source",isValidConnection:s,doc:A,lib:k,flowId:m,nodeLookup:i});M=p.handleDomNode,K=p.connection,f=!!z,h=p.isValid,j=null,h?j=!0:f&&!h&&(j=!1),L=j;let q={...P,isValid:L,to:p.toHandle&&L?ca({x:p.toHandle.x,y:p.toHandle.y},o):I,toHandle:p.toHandle,toPosition:L&&p.toHandle?p.toHandle.position:bN[N.position],toNode:p.toHandle?i.get(p.toHandle.nodeId):null};L&&z&&P.toHandle&&q.toHandle&&P.toHandle.type===q.toHandle.type&&P.toHandle.nodeId===q.toHandle.nodeId&&P.toHandle.id===q.toHandle.id&&P.to.x===q.to.x&&P.to.y===q.to.y||(u(q),P=q)}function S(a){if(G){(z||M)&&K&&L&&q?.(K);let{inProgress:b,...c}=P,d={...c,toPosition:P.toHandle?P.toPosition:null};r?.(a,d),f&&t?.(a,d)}o(),cancelAnimationFrame(B),J=!1,L=!1,K=null,M=null,A.removeEventListener("mousemove",R),A.removeEventListener("mouseup",S),A.removeEventListener("touchmove",R),A.removeEventListener("touchend",S)}0===y&&Q(),A.addEventListener("mousemove",R),A.addEventListener("mouseup",S),A.addEventListener("touchmove",R),A.addEventListener("touchend",S)},isValid:cQ};!function(a){a.Line="line",a.Handle="handle"}(k||(k={}));var cS=c(9733);let cT=a=>{let b,c=new Set,d=(a,d)=>{let e="function"==typeof a?a(b):a;if(!Object.is(e,b)){let a=b;b=(null!=d?d:"object"!=typeof e||null===e)?e:Object.assign({},b,e),c.forEach(c=>c(b,a))}},e=()=>b,f={setState:d,getState:e,getInitialState:()=>g,subscribe:a=>(c.add(a),()=>c.delete(a)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),c.clear()}},g=b=a(d,e,f);return f},{useDebugValue:cU}=n,{useSyncExternalStoreWithSelector:cV}=cS,cW=a=>a;function cX(a,b=cW,c){let d=cV(a.subscribe,a.getState,a.getServerState||a.getInitialState,b,c);return cU(d),d}let cY=(a,b)=>{let c=(a=>a?cT(a):cT)(a),d=(a,d=b)=>cX(c,a,d);return Object.assign(d,c),d};function cZ(a,b){if(Object.is(a,b))return!0;if("object"!=typeof a||null===a||"object"!=typeof b||null===b)return!1;if(a instanceof Map&&b instanceof Map){if(a.size!==b.size)return!1;for(let[c,d]of a)if(!Object.is(d,b.get(c)))return!1;return!0}if(a instanceof Set&&b instanceof Set){if(a.size!==b.size)return!1;for(let c of a)if(!b.has(c))return!1;return!0}let c=Object.keys(a);if(c.length!==Object.keys(b).length)return!1;for(let d of c)if(!Object.prototype.hasOwnProperty.call(b,d)||!Object.is(a[d],b[d]))return!1;return!0}c(1215);let c$=(0,n.createContext)(null),c_=c$.Provider,c0=bI.error001();function c1(a,b){let c=(0,n.useContext)(c$);if(null===c)throw Error(c0);return cX(c,a,b)}function c2(){let a=(0,n.useContext)(c$);if(null===a)throw Error(c0);return(0,n.useMemo)(()=>({getState:a.getState,setState:a.setState,subscribe:a.subscribe}),[a])}let c3={display:"none"},c4={position:"absolute",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)",clipPath:"inset(100%)"},c5="react-flow__node-desc",c6="react-flow__edge-desc",c7=a=>a.ariaLiveMessage,c8=a=>a.ariaLabelConfig;function c9({rfId:a}){let b=c1(c7);return(0,m.jsx)("div",{id:`react-flow__aria-live-${a}`,"aria-live":"assertive","aria-atomic":"true",style:c4,children:b})}function da({rfId:a,disableKeyboardA11y:b}){let c=c1(c8);return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("div",{id:`${c5}-${a}`,style:c3,children:b?c["node.a11yDescription.default"]:c["node.a11yDescription.keyboardDisabled"]}),(0,m.jsx)("div",{id:`${c6}-${a}`,style:c3,children:c["edge.a11yDescription.default"]}),!b&&(0,m.jsx)(c9,{rfId:a})]})}let db=(0,n.forwardRef)(({position:a="top-left",children:b,className:c,style:d,...e},f)=>{let g=`${a}`.split("-");return(0,m.jsx)("div",{className:z(["react-flow__panel",c,...g]),style:d,ref:f,...e,children:b})});function dc({proOptions:a,position:b="bottom-right"}){return a?.hideAttribution?null:(0,m.jsx)(db,{position:b,className:"react-flow__attribution","data-message":"Please only hide this attribution when you are subscribed to React Flow Pro: https://pro.reactflow.dev",children:(0,m.jsx)("a",{href:"https://reactflow.dev",target:"_blank",rel:"noopener noreferrer","aria-label":"React Flow attribution",children:"React Flow"})})}db.displayName="Panel";let dd=a=>{let b=[],c=[];for(let[,c]of a.nodeLookup)c.selected&&b.push(c.internals.userNode);for(let[,b]of a.edgeLookup)b.selected&&c.push(b);return{selectedNodes:b,selectedEdges:c}},de=a=>a.id;function df(a,b){return cZ(a.selectedNodes.map(de),b.selectedNodes.map(de))&&cZ(a.selectedEdges.map(de),b.selectedEdges.map(de))}function dg({onSelectionChange:a}){c2();let{selectedNodes:b,selectedEdges:c}=c1(dd,df);return null}let dh=a=>!!a.onSelectionChangeHandlers;function di({onSelectionChange:a}){let b=c1(dh);return a||b?(0,m.jsx)(dg,{onSelectionChange:a}):null}let dj=[0,0],dk={x:0,y:0,zoom:1},dl=["nodes","edges","defaultNodes","defaultEdges","onConnect","onConnectStart","onConnectEnd","onClickConnectStart","onClickConnectEnd","nodesDraggable","autoPanOnNodeFocus","nodesConnectable","nodesFocusable","edgesFocusable","edgesReconnectable","elevateNodesOnSelect","elevateEdgesOnSelect","minZoom","maxZoom","nodeExtent","onNodesChange","onEdgesChange","elementsSelectable","connectionMode","snapGrid","snapToGrid","translateExtent","connectOnClick","defaultEdgeOptions","fitView","fitViewOptions","onNodesDelete","onEdgesDelete","onDelete","onNodeDrag","onNodeDragStart","onNodeDragStop","onSelectionDrag","onSelectionDragStart","onSelectionDragStop","onMoveStart","onMove","onMoveEnd","noPanClassName","nodeOrigin","autoPanOnConnect","autoPanOnNodeDrag","onError","connectionRadius","isValidConnection","selectNodesOnDrag","nodeDragThreshold","connectionDragThreshold","onBeforeDelete","debug","autoPanSpeed","paneClickDistance","ariaLabelConfig","rfId"],dm=a=>({setNodes:a.setNodes,setEdges:a.setEdges,setMinZoom:a.setMinZoom,setMaxZoom:a.setMaxZoom,setTranslateExtent:a.setTranslateExtent,setNodeExtent:a.setNodeExtent,reset:a.reset,setDefaultNodesAndEdges:a.setDefaultNodesAndEdges,setPaneClickDistance:a.setPaneClickDistance}),dn={translateExtent:bJ,nodeOrigin:dj,minZoom:.5,maxZoom:2,elementsSelectable:!0,noPanClassName:"nopan",rfId:"1",paneClickDistance:0};function dp(a){let{setNodes:b,setEdges:c,setMinZoom:d,setMaxZoom:e,setTranslateExtent:f,setNodeExtent:g,reset:h,setDefaultNodesAndEdges:i,setPaneClickDistance:j}=c1(dm,cZ),k=c2(),l=(0,n.useRef)(dn);return(0,n.useEffect)(()=>{for(let h of dl){let i=a[h];if(i!==l.current[h]&&void 0!==a[h])if("nodes"===h)b(i);else if("edges"===h)c(i);else if("minZoom"===h)d(i);else if("maxZoom"===h)e(i);else if("translateExtent"===h)f(i);else if("nodeExtent"===h)g(i);else if("paneClickDistance"===h)j(i);else if("ariaLabelConfig"===h)k.setState({ariaLabelConfig:{...bL,...i||{}}});else"fitView"===h?k.setState({fitViewQueued:i}):"fitViewOptions"===h?k.setState({fitViewOptions:i}):k.setState({[h]:i})}l.current=a},dl.map(b=>a[b])),null}let dq="undefined"!=typeof document?document:null;function dr(a=null,b={target:dq,actInsideInputWithModifier:!0}){let[c,d]=(0,n.useState)(!1);(0,n.useRef)(!1),(0,n.useRef)(new Set([]));let[e,f]=(0,n.useMemo)(()=>{if(null!==a){let b=(Array.isArray(a)?a:[a]).filter(a=>"string"==typeof a).map(a=>a.replace("+","\n").replace("\n\n","\n+").split("\n")),c=b.reduce((a,b)=>a.concat(...b),[]);return[b,c]}return[[],[]]},[a]);return c}function ds(a,b){let c=[],d=new Map,e=[];for(let b of a)if("add"===b.type){e.push(b);continue}else if("remove"===b.type||"replace"===b.type)d.set(b.id,[b]);else{let a=d.get(b.id);a?a.push(b):d.set(b.id,[b])}for(let a of b){let b=d.get(a.id);if(!b){c.push(a);continue}if("remove"===b[0].type)continue;if("replace"===b[0].type){c.push({...b[0].item});continue}let e={...a};for(let a of b){var f=a,g=e;switch(f.type){case"select":g.selected=f.selected;break;case"position":void 0!==f.position&&(g.position=f.position),void 0!==f.dragging&&(g.dragging=f.dragging);break;case"dimensions":void 0!==f.dimensions&&(g.measured??={},g.measured.width=f.dimensions.width,g.measured.height=f.dimensions.height,f.setAttributes&&((!0===f.setAttributes||"width"===f.setAttributes)&&(g.width=f.dimensions.width),(!0===f.setAttributes||"height"===f.setAttributes)&&(g.height=f.dimensions.height))),"boolean"==typeof f.resizing&&(g.resizing=f.resizing)}}c.push(e)}return e.length&&e.forEach(a=>{void 0!==a.index?c.splice(a.index,0,{...a.item}):c.push({...a.item})}),c}function dt(a,b){return{id:a,type:"select",selected:b}}function du(a,b=new Set,c=!1){let d=[];for(let[e,f]of a){let a=b.has(e);(void 0!==f.selected||a)&&f.selected!==a&&(c&&(f.selected=a),d.push(dt(f.id,a)))}return d}function dv({items:a=[],lookup:b}){let c=[],d=new Map(a.map(a=>[a.id,a]));for(let[d,e]of a.entries()){let a=b.get(e.id),f=a?.internals?.userNode??a;void 0!==f&&f!==e&&c.push({id:e.id,item:e,type:"replace"}),void 0===f&&c.push({item:e,type:"add",index:d})}for(let[a]of b)void 0===d.get(a)&&c.push({id:a,type:"remove"});return c}function dw(a){return{id:a.id,type:"remove"}}let dx=a=>(a=>"id"in a&&"position"in a&&!("source"in a)&&!("target"in a))(a);function dy(a){return(0,n.forwardRef)(a)}let dz=n.useEffect;function dA(a){let[b,c]=(0,n.useState)(BigInt(0)),[d]=(0,n.useState)(()=>{var a;let b;return a=()=>c(a=>a+BigInt(1)),b=[],{get:()=>b,reset:()=>{b=[]},push:c=>{b.push(c),a()}}});return dz(()=>{let b=d.get();b.length&&(a(b),d.reset())},[b]),d}let dB=(0,n.createContext)(null);function dC({children:a}){let b=c2(),c=dA((0,n.useCallback)(a=>{let{nodes:c=[],setNodes:d,hasDefaultNodes:e,onNodesChange:f,nodeLookup:g,fitViewQueued:h}=b.getState(),i=c;for(let b of a)i="function"==typeof b?b(i):b;let j=dv({items:i,lookup:g});e&&d(i),j.length>0?f?.(j):h&&window.requestAnimationFrame(()=>{let{fitViewQueued:a,nodes:c,setNodes:d}=b.getState();a&&d(c)})},[])),d=dA((0,n.useCallback)(a=>{let{edges:c=[],setEdges:d,hasDefaultEdges:e,onEdgesChange:f,edgeLookup:g}=b.getState(),h=c;for(let b of a)h="function"==typeof b?b(h):b;e?d(h):f&&f(dv({items:h,lookup:g}))},[])),e=(0,n.useMemo)(()=>({nodeQueue:c,edgeQueue:d}),[]);return(0,m.jsx)(dB.Provider,{value:e,children:a})}let dD=a=>!!a.panZoom;function dE(){let a=(()=>{let a=c2();return(0,n.useMemo)(()=>({zoomIn:b=>{let{panZoom:c}=a.getState();return c?c.scaleBy(1.2,{duration:b?.duration}):Promise.resolve(!1)},zoomOut:b=>{let{panZoom:c}=a.getState();return c?c.scaleBy(1/1.2,{duration:b?.duration}):Promise.resolve(!1)},zoomTo:(b,c)=>{let{panZoom:d}=a.getState();return d?d.scaleTo(b,{duration:c?.duration}):Promise.resolve(!1)},getZoom:()=>a.getState().transform[2],setViewport:async(b,c)=>{let{transform:[d,e,f],panZoom:g}=a.getState();return g?(await g.setViewport({x:b.x??d,y:b.y??e,zoom:b.zoom??f},c),Promise.resolve(!0)):Promise.resolve(!1)},getViewport:()=>{let[b,c,d]=a.getState().transform;return{x:b,y:c,zoom:d}},setCenter:async(b,c,d)=>a.getState().setCenter(b,c,d),fitBounds:async(b,c)=>{let{width:d,height:e,minZoom:f,maxZoom:g,panZoom:h}=a.getState(),i=cc(b,d,e,f,g,c?.padding??.1);return h?(await h.setViewport(i,{duration:c?.duration,ease:c?.ease,interpolate:c?.interpolate}),Promise.resolve(!0)):Promise.resolve(!1)},screenToFlowPosition:(b,c={})=>{let{transform:d,snapGrid:e,snapToGrid:f,domNode:g}=a.getState();if(!g)return b;let{x:h,y:i}=g.getBoundingClientRect(),j={x:b.x-h,y:b.y-i},k=c.snapGrid??e;return b9(j,d,c.snapToGrid??f,k)},flowToScreenPosition:b=>{let{transform:c,domNode:d}=a.getState();if(!d)return b;let{x:e,y:f}=d.getBoundingClientRect(),g=ca(b,c);return{x:g.x+e,y:g.y+f}}}),[])})(),b=c2(),c=function(){let a=(0,n.useContext)(dB);if(!a)throw Error("useBatchContext must be used within a BatchProvider");return a}(),d=c1(dD),e=(0,n.useMemo)(()=>{let a=a=>b.getState().nodeLookup.get(a),d=a=>{c.nodeQueue.push(a)},e=a=>{c.edgeQueue.push(a)},f=a=>{let{nodeLookup:c,nodeOrigin:d}=b.getState(),e=dx(a)?a:c.get(a.id),f=e.parentId?function(a,b={width:0,height:0},c,d,e){let f={...a},g=d.get(c);if(g){let a=g.origin||e;f.x+=g.internals.positionAbsolute.x-(b.width??0)*a[0],f.y+=g.internals.positionAbsolute.y-(b.height??0)*a[1]}return f}(e.position,e.measured,e.parentId,c,d):e.position;return b1({...e,position:f,width:e.measured?.width??e.width,height:e.measured?.height??e.height})},g=(a,b,c={replace:!1})=>{d(d=>d.map(d=>{if(d.id===a){let a="function"==typeof b?b(d):b;return c.replace&&dx(a)?a:{...d,...a}}return d}))},h=(a,b,c={replace:!1})=>{e(d=>d.map(d=>{if(d.id===a){let a="function"==typeof b?b(d):b;return c.replace&&bP(a)?a:{...d,...a}}return d}))};return{getNodes:()=>b.getState().nodes.map(a=>({...a})),getNode:b=>a(b)?.internals.userNode,getInternalNode:a,getEdges:()=>{let{edges:a=[]}=b.getState();return a.map(a=>({...a}))},getEdge:a=>b.getState().edgeLookup.get(a),setNodes:d,setEdges:e,addNodes:a=>{let b=Array.isArray(a)?a:[a];c.nodeQueue.push(a=>[...a,...b])},addEdges:a=>{let b=Array.isArray(a)?a:[a];c.edgeQueue.push(a=>[...a,...b])},toObject:()=>{let{nodes:a=[],edges:c=[],transform:d}=b.getState(),[e,f,g]=d;return{nodes:a.map(a=>({...a})),edges:c.map(a=>({...a})),viewport:{x:e,y:f,zoom:g}}},deleteElements:async({nodes:a=[],edges:c=[]})=>{let{nodes:d,edges:e,onNodesDelete:f,onEdgesDelete:g,triggerNodeChanges:h,triggerEdgeChanges:i,onDelete:j,onBeforeDelete:k}=b.getState(),{nodes:l,edges:m}=await bV({nodesToRemove:a,edgesToRemove:c,nodes:d,edges:e,onBeforeDelete:k}),n=m.length>0,o=l.length>0;if(n){let a=m.map(dw);g?.(m),i(a)}if(o){let a=l.map(dw);f?.(l),h(a)}return(o||n)&&j?.({nodes:l,edges:m}),{deletedNodes:l,deletedEdges:m}},getIntersectingNodes:(a,c=!0,d)=>{let e=b5(a),g=e?a:f(a),h=void 0!==d;return g?(d||b.getState().nodes).filter(d=>{let f=b.getState().nodeLookup.get(d.id);if(f&&!e&&(d.id===a.id||!f.internals.positionAbsolute))return!1;let i=b1(h?d:f),j=b4(i,g);return c&&j>0||j>=i.width*i.height||j>=g.width*g.height}):[]},isNodeIntersecting:(a,b,c=!0)=>{let d=b5(a)?a:f(a);if(!d)return!1;let e=b4(d,b);return c&&e>0||e>=d.width*d.height},updateNode:g,updateNodeData:(a,b,c={replace:!1})=>{g(a,a=>{let d="function"==typeof b?b(a):b;return c.replace?{...a,data:d}:{...a,data:{...a.data,...d}}},c)},updateEdge:h,updateEdgeData:(a,b,c={replace:!1})=>{h(a,a=>{let d="function"==typeof b?b(a):b;return c.replace?{...a,data:d}:{...a,data:{...a.data,...d}}},c)},getNodesBounds:a=>{let{nodeLookup:c,nodeOrigin:d}=b.getState();return((a,b={nodeOrigin:[0,0]})=>0===a.length?{x:0,y:0,width:0,height:0}:b0(a.reduce((a,c)=>{let d="string"==typeof c,e=b.nodeLookup||d?void 0:c;return b.nodeLookup&&(e=d?b.nodeLookup.get(c):bQ(c)?c:b.nodeLookup.get(c.id)),b$(a,e?b2(e,b.nodeOrigin):{x:0,y:0,x2:0,y2:0})},{x:1/0,y:1/0,x2:-1/0,y2:-1/0})))(a,{nodeLookup:c,nodeOrigin:d})},getHandleConnections:({type:a,id:c,nodeId:d})=>Array.from(b.getState().connectionLookup.get(`${d}-${a}${c?`-${c}`:""}`)?.values()??[]),getNodeConnections:({type:a,handleId:c,nodeId:d})=>Array.from(b.getState().connectionLookup.get(`${d}${a?c?`-${a}-${c}`:`-${a}`:""}`)?.values()??[]),fitView:async a=>{let d,e,f=b.getState().fitViewResolver??{promise:new Promise((a,b)=>{d=a,e=b}),resolve:d,reject:e};return b.setState({fitViewQueued:!0,fitViewOptions:a,fitViewResolver:f}),c.nodeQueue.push(a=>[...a]),f.promise}}},[]);return(0,n.useMemo)(()=>({...e,...a,viewportInitialized:d}),[d])}let dF=void 0,dG={position:"absolute",width:"100%",height:"100%",top:0,left:0},dH=a=>({userSelectionActive:a.userSelectionActive,lib:a.lib});function dI({onPaneContextMenu:a,zoomOnScroll:b=!0,zoomOnPinch:c=!0,panOnScroll:d=!1,panOnScrollSpeed:e=.5,panOnScrollMode:g=f.Free,zoomOnDoubleClick:h=!0,panOnDrag:i=!0,defaultViewport:j,translateExtent:k,minZoom:l,maxZoom:o,zoomActivationKeyCode:p,preventScrolling:q=!0,children:r,noWheelClassName:s,noPanClassName:t,onViewportChange:u,isControlledViewport:v,paneClickDistance:w}){let x=c2(),y=(0,n.useRef)(null),{userSelectionActive:z,lib:A}=c1(dH,cZ);return dr(p),(0,n.useRef)(),c2(),(0,n.useCallback)(a=>{u?.({x:a[0],y:a[1],zoom:a[2]}),v||x.setState({transform:a})},[u,v]),(0,m.jsx)("div",{className:"react-flow__renderer",ref:y,style:dG,children:r})}let dJ=a=>({userSelectionActive:a.userSelectionActive,userSelectionRect:a.userSelectionRect});function dK(){let{userSelectionActive:a,userSelectionRect:b}=c1(dJ,cZ);return a&&b?(0,m.jsx)("div",{className:"react-flow__selection react-flow__container",style:{width:b.width,height:b.height,transform:`translate(${b.x}px, ${b.y}px)`}}):null}let dL=(a,b)=>c=>{c.target===b.current&&a?.(c)},dM=a=>({userSelectionActive:a.userSelectionActive,elementsSelectable:a.elementsSelectable,connectionInProgress:a.connection.inProgress,dragging:a.paneDragging});function dN({isSelecting:a,selectionKeyPressed:b,selectionMode:c=g.Full,panOnDrag:d,selectionOnDrag:e,onSelectionStart:f,onSelectionEnd:h,onPaneClick:i,onPaneContextMenu:j,onPaneScroll:k,onPaneMouseEnter:l,onPaneMouseMove:o,onPaneMouseLeave:p,children:q}){let r=c2(),{userSelectionActive:s,elementsSelectable:t,dragging:u,connectionInProgress:v}=c1(dM,cZ),w=t&&(a||s),x=(0,n.useRef)(null),y=(0,n.useRef)(),A=(0,n.useRef)(new Set),B=(0,n.useRef)(new Set),C=(0,n.useRef)(!1),D=(0,n.useRef)(!1),E=a=>{if(C.current||v){C.current=!1;return}i?.(a),r.getState().resetSelectedElements(),r.setState({nodesSelectionActive:!1})},F=!0===d||Array.isArray(d)&&d.includes(0);return(0,m.jsxs)("div",{className:z(["react-flow__pane",{draggable:F,dragging:u,selection:a}]),onClick:w?void 0:dL(E,x),onContextMenu:dL(a=>{if(Array.isArray(d)&&d?.includes(2))return void a.preventDefault();j?.(a)},x),onWheel:dL(k?a=>k(a):void 0,x),onPointerEnter:w?void 0:l,onPointerDown:w?b=>{let{resetSelectedElements:c,domNode:d}=r.getState();if(y.current=d?.getBoundingClientRect(),!t||!a||0!==b.button||b.target!==x.current||!y.current)return;b.target?.setPointerCapture?.(b.pointerId),D.current=!0,C.current=!1;let{x:e,y:g}=cl(b.nativeEvent,y.current);c(),r.setState({userSelectionRect:{width:0,height:0,startX:e,startY:g,x:e,y:g}}),f?.(b)}:o,onPointerMove:w?a=>{let{userSelectionRect:b,transform:d,nodeLookup:e,edgeLookup:f,connectionLookup:h,triggerNodeChanges:i,triggerEdgeChanges:j,defaultEdgeOptions:k}=r.getState();if(!y.current||!b)return;C.current=!0;let{x:l,y:m}=cl(a.nativeEvent,y.current),{startX:n,startY:o}=b,p={startX:n,startY:o,x:l<n?l:n,y:m<o?m:o,width:Math.abs(l-n),height:Math.abs(m-o)},q=A.current,s=B.current;A.current=new Set(bT(e,p,d,c===g.Partial,!0).map(a=>a.id)),B.current=new Set;let t=k?.selectable??!0;for(let a of A.current){let b=h.get(a);if(b)for(let{edgeId:a}of b.values()){let b=f.get(a);b&&(b.selectable??t)&&B.current.add(a)}}ch(q,A.current)||i(du(e,A.current,!0)),ch(s,B.current)||j(du(f,B.current)),r.setState({userSelectionRect:p,userSelectionActive:!0,nodesSelectionActive:!1})}:o,onPointerUp:w?a=>{if(0!==a.button||!D.current)return;a.target?.releasePointerCapture?.(a.pointerId);let{userSelectionRect:c}=r.getState();!s&&c&&a.target===x.current&&E?.(a),r.setState({userSelectionActive:!1,userSelectionRect:null,nodesSelectionActive:A.current.size>0}),h?.(a),(b||e)&&(C.current=!1),D.current=!1}:void 0,onPointerLeave:p,ref:x,style:dG,children:[q,(0,m.jsx)(dK,{})]})}function dO({id:a,store:b,unselect:c=!1,nodeRef:d}){let{addSelectedNodes:e,unselectNodesAndEdges:f,multiSelectionActive:g,nodeLookup:h,onError:i}=b.getState(),j=h.get(a);if(!j)return void i?.("012",bI.error012(a));b.setState({nodesSelectionActive:!1}),j.selected?(c||j.selected&&g)&&(f({nodes:[j],edges:[]}),requestAnimationFrame(()=>d?.current?.blur())):e([a])}function dP({nodeRef:a,disabled:b=!1,noDragClassName:c,handleSelector:d,nodeId:e,isSelectable:f,nodeClickDistance:g}){c2();let[h,i]=(0,n.useState)(!1);return(0,n.useRef)(),h}function dQ(){let a=c2();return(0,n.useCallback)(b=>{let{nodeExtent:c,snapToGrid:d,snapGrid:e,nodesDraggable:f,onError:g,updateNodePositions:h,nodeLookup:i,nodeOrigin:j}=a.getState(),k=new Map,l=a=>a.selected&&(a.draggable||f&&void 0===a.draggable),m=d?e[0]:5,n=d?e[1]:5,o=b.direction.x*m*b.factor,p=b.direction.y*n*b.factor;for(let[,a]of i){if(!l(a))continue;let b={x:a.internals.positionAbsolute.x+o,y:a.internals.positionAbsolute.y+p};d&&(b=b8(b,e));let{position:f,positionAbsolute:h}=function({nodeId:a,nextPosition:b,nodeLookup:c,nodeOrigin:d=[0,0],nodeExtent:e,onError:f}){let g=c.get(a),h=g.parentId?c.get(g.parentId):void 0,{x:i,y:j}=h?h.internals.positionAbsolute:{x:0,y:0},k=g.origin??d,l=g.extent||e;if("parent"!==g.extent||g.expandParent)h&&ce(g.extent)&&(l=[[g.extent[0][0]+i,g.extent[0][1]+j],[g.extent[1][0]+i,g.extent[1][1]+j]]);else if(h){let a=h.measured.width,b=h.measured.height;a&&b&&(l=[[i,j],[i+a,j+b]])}else f?.("005",bI.error005());let m=ce(l)?bX(b,l,g.measured):b;return(void 0===g.measured.width||void 0===g.measured.height)&&f?.("015",bI.error015()),{position:{x:m.x-i+(g.measured.width??0)*k[0],y:m.y-j+(g.measured.height??0)*k[1]},positionAbsolute:m}}({nodeId:a.id,nextPosition:b,nodeLookup:i,nodeExtent:c,nodeOrigin:j,onError:g});a.position=f,a.internals.positionAbsolute=h,k.set(a.id,a)}h(k)},[])}let dR=(0,n.createContext)(null),dS=dR.Provider;dR.Consumer;let dT=a=>({connectOnClick:a.connectOnClick,noPanClassName:a.noPanClassName,rfId:a.rfId}),dU=(0,n.memo)(dy(function({type:a="source",position:b=j.Top,isValidConnection:c,isConnectable:d=!0,isConnectableStart:f=!0,isConnectableEnd:g=!0,id:h,onConnect:i,children:k,className:l,onMouseDown:o,onTouchStart:p,...q},r){let s=h||null,t="target"===a,u=c2(),v=(0,n.useContext)(dR),{connectOnClick:w,noPanClassName:x,rfId:y}=c1(dT,cZ),{connectingFrom:A,connectingTo:B,clickConnecting:C,isPossibleEndHandle:D,connectionInProcess:E,clickConnectionInProcess:F,valid:G}=c1(b=>{let{connectionClickStartHandle:c,connectionMode:d,connection:f}=b,{fromHandle:g,toHandle:h,isValid:i}=f,j=h?.nodeId===v&&h?.id===s&&h?.type===a;return{connectingFrom:g?.nodeId===v&&g?.id===s&&g?.type===a,connectingTo:j,clickConnecting:c?.nodeId===v&&c?.id===s&&c?.type===a,isPossibleEndHandle:d===e.Strict?g?.type!==a:v!==g?.nodeId||s!==g?.id,connectionInProcess:!!g,clickConnectionInProcess:!!c,valid:j&&i}},cZ);v||u.getState().onError?.("010",bI.error010());let H=a=>{let{defaultEdgeOptions:b,onConnect:c,hasDefaultEdges:d}=u.getState(),e={...b,...a};if(d){let{edges:a,setEdges:b}=u.getState();b(cs(e,a))}c?.(e),i?.(e)},I=a=>{if(!v)return;let b="clientX"in a.nativeEvent;if(f&&(b&&0===a.button||!b)){let b=u.getState();cR.onPointerDown(a.nativeEvent,{autoPanOnConnect:b.autoPanOnConnect,connectionMode:b.connectionMode,connectionRadius:b.connectionRadius,domNode:b.domNode,nodeLookup:b.nodeLookup,lib:b.lib,isTarget:t,handleId:s,nodeId:v,flowId:b.rfId,panBy:b.panBy,cancelConnection:b.cancelConnection,onConnectStart:b.onConnectStart,onConnectEnd:b.onConnectEnd,updateConnection:b.updateConnection,onConnect:H,isValidConnection:c||b.isValidConnection,getTransform:()=>u.getState().transform,getFromHandle:()=>u.getState().connection.fromHandle,autoPanSpeed:b.autoPanSpeed,dragThreshold:b.connectionDragThreshold})}b?o?.(a):p?.(a)};return(0,m.jsx)("div",{"data-handleid":s,"data-nodeid":v,"data-handlepos":b,"data-id":`${y}-${v}-${s}-${a}`,className:z(["react-flow__handle",`react-flow__handle-${b}`,"nodrag",x,l,{source:!t,target:t,connectable:d,connectablestart:f,connectableend:g,clickconnecting:C,connectingfrom:A,connectingto:B,valid:G,connectionindicator:d&&(!E||D)&&(E||F?g:f)}]),onMouseDown:I,onTouchStart:I,onClick:w?b=>{let{onClickConnectStart:d,onClickConnectEnd:e,connectionClickStartHandle:g,connectionMode:h,isValidConnection:i,lib:j,rfId:k,nodeLookup:l,connection:m}=u.getState();if(!v||!g&&!f)return;if(!g){d?.(b.nativeEvent,{nodeId:v,handleId:s,handleType:a}),u.setState({connectionClickStartHandle:{nodeId:v,type:a,id:s}});return}let n=cj(b.target),o=c||i,{connection:p,isValid:q}=cR.isValid(b.nativeEvent,{handle:{nodeId:v,id:s,type:a},connectionMode:h,fromNodeId:g.nodeId,fromHandleId:g.id||null,fromType:g.type,isValidConnection:o,flowId:k,doc:n,lib:j,nodeLookup:l});q&&p&&H(p);let r=structuredClone(m);delete r.inProgress,r.toPosition=r.toHandle?r.toHandle.position:null,e?.(b,r),u.setState({connectionClickStartHandle:null})}:void 0,ref:r,...q,children:k})})),dV={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}},dW={input:function({data:a,isConnectable:b,sourcePosition:c=j.Bottom}){return(0,m.jsxs)(m.Fragment,{children:[a?.label,(0,m.jsx)(dU,{type:"source",position:c,isConnectable:b})]})},default:function({data:a,isConnectable:b,targetPosition:c=j.Top,sourcePosition:d=j.Bottom}){return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(dU,{type:"target",position:c,isConnectable:b}),a?.label,(0,m.jsx)(dU,{type:"source",position:d,isConnectable:b})]})},output:function({data:a,isConnectable:b,targetPosition:c=j.Top}){return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(dU,{type:"target",position:c,isConnectable:b}),a?.label]})},group:function(){return null}},dX=a=>{let{width:b,height:c,x:d,y:e}=bS(a.nodeLookup,{filter:a=>!!a.selected});return{width:b6(b)?b:null,height:b6(c)?c:null,userSelectionActive:a.userSelectionActive,transformString:`translate(${a.transform[0]}px,${a.transform[1]}px) scale(${a.transform[2]}) translate(${d}px,${e}px)`}};function dY({onSelectionContextMenu:a,noPanClassName:b,disableKeyboardA11y:c}){let d=c2(),{width:e,height:f,transformString:g,userSelectionActive:h}=c1(dX,cZ),i=dQ(),j=(0,n.useRef)(null);if(dP({nodeRef:j}),h||!e||!f)return null;let k=a?b=>{a(b,d.getState().nodes.filter(a=>a.selected))}:void 0;return(0,m.jsx)("div",{className:z(["react-flow__nodesselection","react-flow__container",b]),style:{transform:g},children:(0,m.jsx)("div",{ref:j,className:"react-flow__nodesselection-rect",onContextMenu:k,tabIndex:c?void 0:-1,onKeyDown:c?void 0:a=>{Object.prototype.hasOwnProperty.call(dV,a.key)&&(a.preventDefault(),i({direction:dV[a.key],factor:a.shiftKey?4:1}))},style:{width:e,height:f}})})}let dZ=void 0,d$=a=>({nodesSelectionActive:a.nodesSelectionActive,userSelectionActive:a.userSelectionActive});function d_({children:a,onPaneClick:b,onPaneMouseEnter:c,onPaneMouseMove:d,onPaneMouseLeave:e,onPaneContextMenu:f,onPaneScroll:g,paneClickDistance:h,deleteKeyCode:i,selectionKeyCode:j,selectionOnDrag:k,selectionMode:l,onSelectionStart:n,onSelectionEnd:o,multiSelectionKeyCode:p,panActivationKeyCode:q,zoomActivationKeyCode:r,elementsSelectable:s,zoomOnScroll:t,zoomOnPinch:u,panOnScroll:v,panOnScrollSpeed:w,panOnScrollMode:x,zoomOnDoubleClick:y,panOnDrag:z,defaultViewport:A,translateExtent:B,minZoom:C,maxZoom:D,preventScrolling:E,onSelectionContextMenu:F,noWheelClassName:G,noPanClassName:H,disableKeyboardA11y:I,onViewportChange:J,isControlledViewport:K}){let{nodesSelectionActive:L,userSelectionActive:M}=c1(d$),N=dr(j,{target:dZ}),O=dr(q,{target:dZ}),P=O||z,Q=O||v,R=k&&!0!==P,S=N||M||R;return!function({deleteKeyCode:a,multiSelectionKeyCode:b}){c2();let{deleteElements:c}=dE();dr(a,{actInsideInputWithModifier:!1}),dr(b,{target:dF})}({deleteKeyCode:i,multiSelectionKeyCode:p}),(0,m.jsx)(dI,{onPaneContextMenu:f,elementsSelectable:s,zoomOnScroll:t,zoomOnPinch:u,panOnScroll:Q,panOnScrollSpeed:w,panOnScrollMode:x,zoomOnDoubleClick:y,panOnDrag:!N&&P,defaultViewport:A,translateExtent:B,minZoom:C,maxZoom:D,zoomActivationKeyCode:r,preventScrolling:E,noWheelClassName:G,noPanClassName:H,onViewportChange:J,isControlledViewport:K,paneClickDistance:h,children:(0,m.jsxs)(dN,{onSelectionStart:n,onSelectionEnd:o,onPaneClick:b,onPaneMouseEnter:c,onPaneMouseMove:d,onPaneMouseLeave:e,onPaneContextMenu:f,onPaneScroll:g,panOnDrag:P,isSelecting:!!S,selectionMode:l,selectionKeyPressed:N,selectionOnDrag:R,children:[a,L&&(0,m.jsx)(dY,{onSelectionContextMenu:F,noPanClassName:H,disableKeyboardA11y:I})]})})}d_.displayName="FlowRenderer";let d0=(0,n.memo)(d_),d1=a=>a.updateNodeInternals;function d2({id:a,onClick:b,onMouseEnter:c,onMouseMove:d,onMouseLeave:e,onContextMenu:f,onDoubleClick:g,nodesDraggable:h,elementsSelectable:i,nodesConnectable:j,nodesFocusable:k,resizeObserver:l,noDragClassName:o,noPanClassName:p,disableKeyboardA11y:q,rfId:r,nodeTypes:s,nodeClickDistance:t,onError:u}){let{node:v,internals:w,isParent:x}=c1(b=>{let c=b.nodeLookup.get(a),d=b.parentLookup.has(a);return{node:c,internals:c.internals,isParent:d}},cZ),y=v.type||"default",A=s?.[y]||dW[y];void 0===A&&(u?.("003",bI.error003(y)),y="default",A=s?.default||dW.default);let B=!!(v.draggable||h&&void 0===v.draggable),C=!!(v.selectable||i&&void 0===v.selectable),D=!!(v.connectable||j&&void 0===v.connectable),E=!!(v.focusable||k&&void 0===v.focusable),F=c2(),G=cg(v),H=function({node:a,nodeType:b,hasDimensions:c,resizeObserver:d}){c2();let e=(0,n.useRef)(null);return(0,n.useRef)(null),(0,n.useRef)(a.sourcePosition),(0,n.useRef)(a.targetPosition),(0,n.useRef)(b),c&&a.internals.handleBounds,e}({node:v,nodeType:y,hasDimensions:G,resizeObserver:l}),I=dP({nodeRef:H,disabled:v.hidden||!B,noDragClassName:o,handleSelector:v.dragHandle,nodeId:a,isSelectable:C,nodeClickDistance:t}),J=dQ();if(v.hidden)return null;let K=cf(v),L=void 0===v.internals.handleBounds?{width:v.width??v.initialWidth??v.style?.width,height:v.height??v.initialHeight??v.style?.height}:{width:v.width??v.style?.width,height:v.height??v.style?.height},M=C||B||b||c||d||e,N=c?a=>c(a,{...w.userNode}):void 0,O=d?a=>d(a,{...w.userNode}):void 0,P=e?a=>e(a,{...w.userNode}):void 0,Q=f?a=>f(a,{...w.userNode}):void 0,R=g?a=>g(a,{...w.userNode}):void 0;return(0,m.jsx)("div",{className:z(["react-flow__node",`react-flow__node-${y}`,{[p]:B},v.className,{selected:v.selected,selectable:C,parent:x,draggable:B,dragging:I}]),ref:H,style:{zIndex:w.z,transform:`translate(${w.positionAbsolute.x}px,${w.positionAbsolute.y}px)`,pointerEvents:M?"all":"none",visibility:G?"visible":"hidden",...v.style,...L},"data-id":a,"data-testid":`rf__node-${a}`,onMouseEnter:N,onMouseMove:O,onMouseLeave:P,onContextMenu:Q,onClick:c=>{let{selectNodesOnDrag:d,nodeDragThreshold:e}=F.getState();C&&(!d||!B||e>0)&&dO({id:a,store:F,nodeRef:H}),b&&b(c,{...w.userNode})},onDoubleClick:R,onKeyDown:E?b=>{if(!function(a){let b=a.composedPath?.()?.[0]||a.target;return b?.nodeType===1&&(ck.includes(b.nodeName)||b.hasAttribute("contenteditable")||!!b.closest(".nokey"))}(b.nativeEvent)&&!q){if(bK.includes(b.key)&&C)dO({id:a,store:F,unselect:"Escape"===b.key,nodeRef:H});else if(B&&v.selected&&Object.prototype.hasOwnProperty.call(dV,b.key)){b.preventDefault();let{ariaLabelConfig:a}=F.getState();F.setState({ariaLiveMessage:a["node.a11yDescription.ariaLiveMessage"]({direction:b.key.replace("Arrow","").toLowerCase(),x:~~w.positionAbsolute.x,y:~~w.positionAbsolute.y})}),J({direction:dV[b.key],factor:b.shiftKey?4:1})}}}:void 0,tabIndex:E?0:void 0,onFocus:E?()=>{if(q||!H.current?.matches(":focus-visible"))return;let{transform:b,width:c,height:d,autoPanOnNodeFocus:e,setCenter:f}=F.getState();e&&(bT(new Map([[a,v]]),{x:0,y:0,width:c,height:d},b,!0).length>0||f(v.position.x+K.width/2,v.position.y+K.height/2,{zoom:b[2]}))}:void 0,role:v.ariaRole??(E?"group":void 0),"aria-roledescription":"node","aria-describedby":q?void 0:`${c5}-${r}`,"aria-label":v.ariaLabel,...v.domAttributes,children:(0,m.jsx)(dS,{value:a,children:(0,m.jsx)(A,{id:a,data:v.data,type:y,positionAbsoluteX:w.positionAbsolute.x,positionAbsoluteY:w.positionAbsolute.y,selected:v.selected??!1,selectable:C,draggable:B,deletable:v.deletable??!0,isConnectable:D,sourcePosition:v.sourcePosition,targetPosition:v.targetPosition,dragging:I,dragHandle:v.dragHandle,zIndex:w.z,parentId:v.parentId,...K})})})}let d3=a=>({nodesDraggable:a.nodesDraggable,nodesConnectable:a.nodesConnectable,nodesFocusable:a.nodesFocusable,elementsSelectable:a.elementsSelectable,onError:a.onError});function d4(a){var b;let{nodesDraggable:c,nodesConnectable:d,nodesFocusable:e,elementsSelectable:f,onError:g}=c1(d3,cZ),h=(b=a.onlyRenderVisibleElements,c1((0,n.useCallback)(a=>b?bT(a.nodeLookup,{x:0,y:0,width:a.width,height:a.height},a.transform,!0).map(a=>a.id):Array.from(a.nodeLookup.keys()),[b]),cZ)),i=function(){let a=c1(d1),[b]=(0,n.useState)(()=>"undefined"==typeof ResizeObserver?null:new ResizeObserver(b=>{let c=new Map;b.forEach(a=>{let b=a.target.getAttribute("data-id");c.set(b,{id:b,nodeElement:a.target,force:!0})}),a(c)}));return b}();return(0,m.jsx)("div",{className:"react-flow__nodes",style:dG,children:h.map(b=>(0,m.jsx)(d2,{id:b,nodeTypes:a.nodeTypes,nodeExtent:a.nodeExtent,onClick:a.onNodeClick,onMouseEnter:a.onNodeMouseEnter,onMouseMove:a.onNodeMouseMove,onMouseLeave:a.onNodeMouseLeave,onContextMenu:a.onNodeContextMenu,onDoubleClick:a.onNodeDoubleClick,noDragClassName:a.noDragClassName,noPanClassName:a.noPanClassName,rfId:a.rfId,disableKeyboardA11y:a.disableKeyboardA11y,resizeObserver:i,nodesDraggable:c,nodesConnectable:d,nodesFocusable:e,elementsSelectable:f,nodeClickDistance:a.nodeClickDistance,onError:g},b))})}d4.displayName="NodeRenderer";let d5=(0,n.memo)(d4),d6={[i.Arrow]:({color:a="none",strokeWidth:b=1})=>(0,m.jsx)("polyline",{style:{stroke:a,strokeWidth:b},strokeLinecap:"round",strokeLinejoin:"round",fill:"none",points:"-5,-4 0,0 -5,4"}),[i.ArrowClosed]:({color:a="none",strokeWidth:b=1})=>(0,m.jsx)("polyline",{style:{stroke:a,fill:a,strokeWidth:b},strokeLinecap:"round",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4 -5,-4"})},d7=({id:a,type:b,color:c,width:d=12.5,height:e=12.5,markerUnits:f="strokeWidth",strokeWidth:g,orient:h="auto-start-reverse"})=>{let i=function(a){let b=c2();return(0,n.useMemo)(()=>Object.prototype.hasOwnProperty.call(d6,a)?d6[a]:(b.getState().onError?.("009",bI.error009(a)),null),[a])}(b);return i?(0,m.jsx)("marker",{className:"react-flow__arrowhead",id:a,markerWidth:`${d}`,markerHeight:`${e}`,viewBox:"-10 -10 20 20",markerUnits:f,orient:h,refX:"0",refY:"0",children:(0,m.jsx)(i,{color:c,strokeWidth:g})}):null},d8=({defaultColor:a,rfId:b})=>{let c=c1(a=>a.edges),d=c1(a=>a.defaultEdgeOptions),e=(0,n.useMemo)(()=>(function(a,{id:b,defaultColor:c,defaultMarkerStart:d,defaultMarkerEnd:e}){let f=new Set;return a.reduce((a,g)=>([g.markerStart||d,g.markerEnd||e].forEach(d=>{if(d&&"object"==typeof d){let e=cC(d,b);f.has(e)||(a.push({id:e,color:d.color||c,...d}),f.add(e))}}),a),[]).sort((a,b)=>a.id.localeCompare(b.id))})(c,{id:b,defaultColor:a,defaultMarkerStart:d?.markerStart,defaultMarkerEnd:d?.markerEnd}),[c,d,b,a]);return e.length?(0,m.jsx)("svg",{className:"react-flow__marker","aria-hidden":"true",children:(0,m.jsx)("defs",{children:e.map(a=>(0,m.jsx)(d7,{id:a.id,type:a.type,color:a.color,width:a.width,height:a.height,markerUnits:a.markerUnits,strokeWidth:a.strokeWidth,orient:a.orient},a.id))})}):null};d8.displayName="MarkerDefinitions";var d9=(0,n.memo)(d8);function ea({x:a,y:b,label:c,labelStyle:d,labelShowBg:e=!0,labelBgStyle:f,labelBgPadding:g=[2,4],labelBgBorderRadius:h=2,children:i,className:j,...k}){let[l,o]=(0,n.useState)({x:1,y:0,width:0,height:0}),p=z(["react-flow__edge-textwrapper",j]),q=(0,n.useRef)(null);return c?(0,m.jsxs)("g",{transform:`translate(${a-l.width/2} ${b-l.height/2})`,className:p,visibility:l.width?"visible":"hidden",...k,children:[e&&(0,m.jsx)("rect",{width:l.width+2*g[0],x:-g[0],y:-g[1],height:l.height+2*g[1],className:"react-flow__edge-textbg",style:f,rx:h,ry:h}),(0,m.jsx)("text",{className:"react-flow__edge-text",y:l.height/2,dy:"0.3em",ref:q,style:d,children:c}),i]}):null}ea.displayName="EdgeText";let eb=(0,n.memo)(ea);function ec({path:a,labelX:b,labelY:c,label:d,labelStyle:e,labelShowBg:f,labelBgStyle:g,labelBgPadding:h,labelBgBorderRadius:i,interactionWidth:j=20,...k}){return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("path",{...k,d:a,fill:"none",className:z(["react-flow__edge-path",k.className])}),j&&(0,m.jsx)("path",{d:a,fill:"none",strokeOpacity:0,strokeWidth:j,className:"react-flow__edge-interaction"}),d&&b6(b)&&b6(c)?(0,m.jsx)(eb,{x:b,y:c,label:d,labelStyle:e,labelShowBg:f,labelBgStyle:g,labelBgPadding:h,labelBgBorderRadius:i}):null]})}function ed({pos:a,x1:b,y1:c,x2:d,y2:e}){return a===j.Left||a===j.Right?[.5*(b+d),c]:[b,.5*(c+e)]}function ee({sourceX:a,sourceY:b,sourcePosition:c=j.Bottom,targetX:d,targetY:e,targetPosition:f=j.Top}){let[g,h]=ed({pos:c,x1:a,y1:b,x2:d,y2:e}),[i,k]=ed({pos:f,x1:d,y1:e,x2:a,y2:b}),[l,m,n,o]=cn({sourceX:a,sourceY:b,targetX:d,targetY:e,sourceControlX:g,sourceControlY:h,targetControlX:i,targetControlY:k});return[`M${a},${b} C${g},${h} ${i},${k} ${d},${e}`,l,m,n,o]}function ef(a){return(0,n.memo)(({id:b,sourceX:c,sourceY:d,targetX:e,targetY:f,sourcePosition:g,targetPosition:h,label:i,labelStyle:j,labelShowBg:k,labelBgStyle:l,labelBgPadding:n,labelBgBorderRadius:o,style:p,markerEnd:q,markerStart:r,interactionWidth:s})=>{let[t,u,v]=ee({sourceX:c,sourceY:d,sourcePosition:g,targetX:e,targetY:f,targetPosition:h}),w=a.isInternal?void 0:b;return(0,m.jsx)(ec,{id:w,path:t,labelX:u,labelY:v,label:i,labelStyle:j,labelShowBg:k,labelBgStyle:l,labelBgPadding:n,labelBgBorderRadius:o,style:p,markerEnd:q,markerStart:r,interactionWidth:s})})}let eg=ef({isInternal:!1}),eh=ef({isInternal:!0});function ei(a){return(0,n.memo)(({id:b,sourceX:c,sourceY:d,targetX:e,targetY:f,label:g,labelStyle:h,labelShowBg:i,labelBgStyle:k,labelBgPadding:l,labelBgBorderRadius:n,style:o,sourcePosition:p=j.Bottom,targetPosition:q=j.Top,markerEnd:r,markerStart:s,pathOptions:t,interactionWidth:u})=>{let[v,w,x]=cx({sourceX:c,sourceY:d,sourcePosition:p,targetX:e,targetY:f,targetPosition:q,borderRadius:t?.borderRadius,offset:t?.offset,stepPosition:t?.stepPosition}),y=a.isInternal?void 0:b;return(0,m.jsx)(ec,{id:y,path:v,labelX:w,labelY:x,label:g,labelStyle:h,labelShowBg:i,labelBgStyle:k,labelBgPadding:l,labelBgBorderRadius:n,style:o,markerEnd:r,markerStart:s,interactionWidth:u})})}eg.displayName="SimpleBezierEdge",eh.displayName="SimpleBezierEdgeInternal";let ej=ei({isInternal:!1}),ek=ei({isInternal:!0});function el(a){return(0,n.memo)(({id:b,...c})=>{let d=a.isInternal?void 0:b;return(0,m.jsx)(ej,{...c,id:d,pathOptions:(0,n.useMemo)(()=>({borderRadius:0,offset:c.pathOptions?.offset}),[c.pathOptions?.offset])})})}ej.displayName="SmoothStepEdge",ek.displayName="SmoothStepEdgeInternal";let em=el({isInternal:!1}),en=el({isInternal:!0});function eo(a){return(0,n.memo)(({id:b,sourceX:c,sourceY:d,targetX:e,targetY:f,label:g,labelStyle:h,labelShowBg:i,labelBgStyle:j,labelBgPadding:k,labelBgBorderRadius:l,style:n,markerEnd:o,markerStart:p,interactionWidth:q})=>{let[r,s,t]=ct({sourceX:c,sourceY:d,targetX:e,targetY:f}),u=a.isInternal?void 0:b;return(0,m.jsx)(ec,{id:u,path:r,labelX:s,labelY:t,label:g,labelStyle:h,labelShowBg:i,labelBgStyle:j,labelBgPadding:k,labelBgBorderRadius:l,style:n,markerEnd:o,markerStart:p,interactionWidth:q})})}em.displayName="StepEdge",en.displayName="StepEdgeInternal";let ep=eo({isInternal:!1}),eq=eo({isInternal:!0});function er(a){return(0,n.memo)(({id:b,sourceX:c,sourceY:d,targetX:e,targetY:f,sourcePosition:g=j.Bottom,targetPosition:h=j.Top,label:i,labelStyle:k,labelShowBg:l,labelBgStyle:n,labelBgPadding:o,labelBgBorderRadius:p,style:q,markerEnd:r,markerStart:s,pathOptions:t,interactionWidth:u})=>{let[v,w,x]=cq({sourceX:c,sourceY:d,sourcePosition:g,targetX:e,targetY:f,targetPosition:h,curvature:t?.curvature}),y=a.isInternal?void 0:b;return(0,m.jsx)(ec,{id:y,path:v,labelX:w,labelY:x,label:i,labelStyle:k,labelShowBg:l,labelBgStyle:n,labelBgPadding:o,labelBgBorderRadius:p,style:q,markerEnd:r,markerStart:s,interactionWidth:u})})}ep.displayName="StraightEdge",eq.displayName="StraightEdgeInternal";let es=er({isInternal:!1}),et=er({isInternal:!0});es.displayName="BezierEdge",et.displayName="BezierEdgeInternal";let eu={default:et,straight:eq,step:en,smoothstep:ek,simplebezier:eh},ev={sourceX:null,sourceY:null,targetX:null,targetY:null,sourcePosition:null,targetPosition:null},ew="react-flow__edgeupdater";function ex({position:a,centerX:b,centerY:c,radius:d=10,onMouseDown:e,onMouseEnter:f,onMouseOut:g,type:h}){return(0,m.jsx)("circle",{onMouseDown:e,onMouseEnter:f,onMouseOut:g,className:z([ew,`${ew}-${h}`]),cx:a===j.Left?b-d:a===j.Right?b+d:b,cy:a===j.Top?c-d:a===j.Bottom?c+d:c,r:d,stroke:"transparent",fill:"transparent"})}function ey({isReconnectable:a,reconnectRadius:b,edge:c,sourceX:d,sourceY:e,targetX:f,targetY:g,sourcePosition:h,targetPosition:i,onReconnect:j,onReconnectStart:k,onReconnectEnd:l,setReconnecting:n,setUpdateHover:o}){let p=c2(),q=(a,b)=>{if(0!==a.button)return;let{autoPanOnConnect:d,domNode:e,isValidConnection:f,connectionMode:g,connectionRadius:h,lib:i,onConnectStart:m,onConnectEnd:o,cancelConnection:q,nodeLookup:r,rfId:s,panBy:t,updateConnection:u}=p.getState(),v="target"===b.type;cR.onPointerDown(a.nativeEvent,{autoPanOnConnect:d,connectionMode:g,connectionRadius:h,domNode:e,handleId:b.id,nodeId:b.nodeId,nodeLookup:r,isTarget:v,edgeUpdaterType:b.type,lib:i,flowId:s,cancelConnection:q,panBy:t,isValidConnection:f,onConnect:a=>j?.(c,a),onConnectStart:(d,e)=>{n(!0),k?.(a,c,b.type),m?.(d,e)},onConnectEnd:o,onReconnectEnd:(a,d)=>{n(!1),l?.(a,c,b.type,d)},updateConnection:u,getTransform:()=>p.getState().transform,getFromHandle:()=>p.getState().connection.fromHandle,dragThreshold:p.getState().connectionDragThreshold})},r=()=>o(!0),s=()=>o(!1);return(0,m.jsxs)(m.Fragment,{children:[(!0===a||"source"===a)&&(0,m.jsx)(ex,{position:h,centerX:d,centerY:e,radius:b,onMouseDown:a=>q(a,{nodeId:c.target,id:c.targetHandle??null,type:"target"}),onMouseEnter:r,onMouseOut:s,type:"source"}),(!0===a||"target"===a)&&(0,m.jsx)(ex,{position:i,centerX:f,centerY:g,radius:b,onMouseDown:a=>q(a,{nodeId:c.source,id:c.sourceHandle??null,type:"source"}),onMouseEnter:r,onMouseOut:s,type:"target"})]})}function ez({id:a,edgesFocusable:b,edgesReconnectable:c,elementsSelectable:d,onClick:f,onDoubleClick:g,onContextMenu:h,onMouseEnter:i,onMouseMove:k,onMouseLeave:l,reconnectRadius:o,onReconnect:p,onReconnectStart:q,onReconnectEnd:r,rfId:s,edgeTypes:t,noPanClassName:u,onError:v,disableKeyboardA11y:w}){let x=c1(b=>b.edgeLookup.get(a)),y=c1(a=>a.defaultEdgeOptions),A=(x=y?{...y,...x}:x).type||"default",B=t?.[A]||eu[A];void 0===B&&(v?.("011",bI.error011(A)),A="default",B=t?.default||eu.default);let C=!!(x.focusable||b&&void 0===x.focusable),D=void 0!==p&&(x.reconnectable||c&&void 0===x.reconnectable),E=!!(x.selectable||d&&void 0===x.selectable),F=(0,n.useRef)(null),[G,H]=(0,n.useState)(!1),[I,J]=(0,n.useState)(!1),K=c2(),{zIndex:L,sourceX:M,sourceY:N,targetX:O,targetY:P,sourcePosition:Q,targetPosition:R}=c1((0,n.useCallback)(b=>{let c=b.nodeLookup.get(x.source),d=b.nodeLookup.get(x.target);if(!c||!d)return{zIndex:x.zIndex,...ev};let f=function(a){let{sourceNode:b,targetNode:c}=a;if(!cy(b)||!cy(c))return null;let d=b.internals.handleBounds||cz(b.handles),f=c.internals.handleBounds||cz(c.handles),g=cB(d?.source??[],a.sourceHandle),h=cB(a.connectionMode===e.Strict?f?.target??[]:(f?.target??[]).concat(f?.source??[]),a.targetHandle);if(!g||!h)return a.onError?.("008",bI.error008(!g?"source":"target",{id:a.id,sourceHandle:a.sourceHandle,targetHandle:a.targetHandle})),null;let i=g?.position||j.Bottom,k=h?.position||j.Top,l=cA(b,g,i),m=cA(c,h,k);return{sourceX:l.x,sourceY:l.y,targetX:m.x,targetY:m.y,sourcePosition:i,targetPosition:k}}({id:a,sourceNode:c,targetNode:d,sourceHandle:x.sourceHandle||null,targetHandle:x.targetHandle||null,connectionMode:b.connectionMode,onError:v});return{zIndex:function({sourceNode:a,targetNode:b,selected:c=!1,zIndex:d,elevateOnSelect:e=!1}){return void 0!==d?d:(e&&c?1e3:0)+Math.max(a.parentId?a.internals.z:0,b.parentId?b.internals.z:0)}({selected:x.selected,zIndex:x.zIndex,sourceNode:c,targetNode:d,elevateOnSelect:b.elevateEdgesOnSelect}),...f||ev}},[x.source,x.target,x.sourceHandle,x.targetHandle,x.selected,x.zIndex]),cZ),S=(0,n.useMemo)(()=>x.markerStart?`url('#${cC(x.markerStart,s)}')`:void 0,[x.markerStart,s]),T=(0,n.useMemo)(()=>x.markerEnd?`url('#${cC(x.markerEnd,s)}')`:void 0,[x.markerEnd,s]);if(x.hidden||null===M||null===N||null===O||null===P)return null;let U=g?a=>{g(a,{...x})}:void 0,V=h?a=>{h(a,{...x})}:void 0,W=i?a=>{i(a,{...x})}:void 0,X=k?a=>{k(a,{...x})}:void 0,Y=l?a=>{l(a,{...x})}:void 0;return(0,m.jsx)("svg",{style:{zIndex:L},children:(0,m.jsxs)("g",{className:z(["react-flow__edge",`react-flow__edge-${A}`,x.className,u,{selected:x.selected,animated:x.animated,inactive:!E&&!f,updating:G,selectable:E}]),onClick:b=>{let{addSelectedEdges:c,unselectNodesAndEdges:d,multiSelectionActive:e}=K.getState();E&&(K.setState({nodesSelectionActive:!1}),x.selected&&e?(d({nodes:[],edges:[x]}),F.current?.blur()):c([a])),f&&f(b,x)},onDoubleClick:U,onContextMenu:V,onMouseEnter:W,onMouseMove:X,onMouseLeave:Y,onKeyDown:C?b=>{if(!w&&bK.includes(b.key)&&E){let{unselectNodesAndEdges:c,addSelectedEdges:d}=K.getState();"Escape"===b.key?(F.current?.blur(),c({edges:[x]})):d([a])}}:void 0,tabIndex:C?0:void 0,role:x.ariaRole??(C?"group":"img"),"aria-roledescription":"edge","data-id":a,"data-testid":`rf__edge-${a}`,"aria-label":null===x.ariaLabel?void 0:x.ariaLabel||`Edge from ${x.source} to ${x.target}`,"aria-describedby":C?`${c6}-${s}`:void 0,ref:F,...x.domAttributes,children:[!I&&(0,m.jsx)(B,{id:a,source:x.source,target:x.target,type:x.type,selected:x.selected,animated:x.animated,selectable:E,deletable:x.deletable??!0,label:x.label,labelStyle:x.labelStyle,labelShowBg:x.labelShowBg,labelBgStyle:x.labelBgStyle,labelBgPadding:x.labelBgPadding,labelBgBorderRadius:x.labelBgBorderRadius,sourceX:M,sourceY:N,targetX:O,targetY:P,sourcePosition:Q,targetPosition:R,data:x.data,style:x.style,sourceHandleId:x.sourceHandle,targetHandleId:x.targetHandle,markerStart:S,markerEnd:T,pathOptions:"pathOptions"in x?x.pathOptions:void 0,interactionWidth:x.interactionWidth}),D&&(0,m.jsx)(ey,{edge:x,isReconnectable:D,reconnectRadius:o,onReconnect:p,onReconnectStart:q,onReconnectEnd:r,sourceX:M,sourceY:N,targetX:O,targetY:P,sourcePosition:Q,targetPosition:R,setUpdateHover:H,setReconnecting:J})]})})}let eA=a=>({edgesFocusable:a.edgesFocusable,edgesReconnectable:a.edgesReconnectable,elementsSelectable:a.elementsSelectable,connectionMode:a.connectionMode,onError:a.onError});function eB({defaultMarkerColor:a,onlyRenderVisibleElements:b,rfId:c,edgeTypes:d,noPanClassName:e,onReconnect:f,onEdgeContextMenu:g,onEdgeMouseEnter:h,onEdgeMouseMove:i,onEdgeMouseLeave:j,onEdgeClick:k,reconnectRadius:l,onEdgeDoubleClick:o,onReconnectStart:p,onReconnectEnd:q,disableKeyboardA11y:r}){let{edgesFocusable:s,edgesReconnectable:t,elementsSelectable:u,onError:v}=c1(eA,cZ),w=c1((0,n.useCallback)(a=>{if(!b)return a.edges.map(a=>a.id);let c=[];if(a.width&&a.height)for(let b of a.edges){let d=a.nodeLookup.get(b.source),e=a.nodeLookup.get(b.target);d&&e&&function({sourceNode:a,targetNode:b,width:c,height:d,transform:e}){let f=b$(b2(a),b2(b));return f.x===f.x2&&(f.x2+=1),f.y===f.y2&&(f.y2+=1),b4({x:-e[0]/e[2],y:-e[1]/e[2],width:c/e[2],height:d/e[2]},b0(f))>0}({sourceNode:d,targetNode:e,width:a.width,height:a.height,transform:a.transform})&&c.push(b.id)}return c},[b]),cZ);return(0,m.jsxs)("div",{className:"react-flow__edges",children:[(0,m.jsx)(d9,{defaultColor:a,rfId:c}),w.map(a=>(0,m.jsx)(ez,{id:a,edgesFocusable:s,edgesReconnectable:t,elementsSelectable:u,noPanClassName:e,onReconnect:f,onContextMenu:g,onMouseEnter:h,onMouseMove:i,onMouseLeave:j,onClick:k,reconnectRadius:l,onDoubleClick:o,onReconnectStart:p,onReconnectEnd:q,rfId:c,onError:v,edgeTypes:d,disableKeyboardA11y:r},a))]})}eB.displayName="EdgeRenderer";let eC=(0,n.memo)(eB),eD=a=>`translate(${a.transform[0]}px,${a.transform[1]}px) scale(${a.transform[2]})`;function eE({children:a}){let b=c1(eD);return(0,m.jsx)("div",{className:"react-flow__viewport xyflow__viewport react-flow__container",style:{transform:b},children:a})}let eF=a=>a.panZoom?.syncViewport;function eG(a){return a.connection.inProgress?{...a.connection,to:b9(a.connection.to,a.transform)}:{...a.connection}}let eH=a=>({nodesConnectable:a.nodesConnectable,isValid:a.connection.isValid,inProgress:a.connection.inProgress,width:a.width,height:a.height});function eI({containerStyle:a,style:b,type:c,component:d}){let{nodesConnectable:e,width:f,height:g,isValid:h,inProgress:i}=c1(eH,cZ);return f&&e&&i?(0,m.jsx)("svg",{style:a,width:f,height:g,className:"react-flow__connectionline react-flow__container",children:(0,m.jsx)("g",{className:z(["react-flow__connection",bO(h)]),children:(0,m.jsx)(eJ,{style:b,type:c,CustomComponent:d,isValid:h})})}):null}let eJ=({style:a,type:b=h.Bezier,CustomComponent:c,isValid:d})=>{let{inProgress:e,from:f,fromNode:g,fromHandle:i,fromPosition:j,to:k,toNode:l,toHandle:n,toPosition:o}=function(a){return c1(eG,cZ)}();if(!e)return;if(c)return(0,m.jsx)(c,{connectionLineType:b,connectionLineStyle:a,fromNode:g,fromHandle:i,fromX:f.x,fromY:f.y,toX:k.x,toY:k.y,fromPosition:j,toPosition:o,connectionStatus:bO(d),toNode:l,toHandle:n});let p="",q={sourceX:f.x,sourceY:f.y,sourcePosition:j,targetX:k.x,targetY:k.y,targetPosition:o};switch(b){case h.Bezier:[p]=cq(q);break;case h.SimpleBezier:[p]=ee(q);break;case h.Step:[p]=cx({...q,borderRadius:0});break;case h.SmoothStep:[p]=cx(q);break;default:[p]=ct(q)}return(0,m.jsx)("path",{d:p,fill:"none",className:"react-flow__connection-path",style:a})};eJ.displayName="ConnectionLine";let eK={};function eL(a=eK){(0,n.useRef)(a),c2()}function eM({nodeTypes:a,edgeTypes:b,onInit:c,onNodeClick:d,onEdgeClick:e,onNodeDoubleClick:f,onEdgeDoubleClick:g,onNodeMouseEnter:h,onNodeMouseMove:i,onNodeMouseLeave:j,onNodeContextMenu:k,onSelectionContextMenu:l,onSelectionStart:o,onSelectionEnd:p,connectionLineType:q,connectionLineStyle:r,connectionLineComponent:s,connectionLineContainerStyle:t,selectionKeyCode:u,selectionOnDrag:v,selectionMode:w,multiSelectionKeyCode:x,panActivationKeyCode:y,zoomActivationKeyCode:z,deleteKeyCode:A,onlyRenderVisibleElements:B,elementsSelectable:C,defaultViewport:D,translateExtent:E,minZoom:F,maxZoom:G,preventScrolling:H,defaultMarkerColor:I,zoomOnScroll:J,zoomOnPinch:K,panOnScroll:L,panOnScrollSpeed:M,panOnScrollMode:N,zoomOnDoubleClick:O,panOnDrag:P,onPaneClick:Q,onPaneMouseEnter:R,onPaneMouseMove:S,onPaneMouseLeave:T,onPaneScroll:U,onPaneContextMenu:V,paneClickDistance:W,nodeClickDistance:X,onEdgeContextMenu:Y,onEdgeMouseEnter:Z,onEdgeMouseMove:$,onEdgeMouseLeave:_,reconnectRadius:aa,onReconnect:ab,onReconnectStart:ac,onReconnectEnd:ad,noDragClassName:ae,noWheelClassName:af,noPanClassName:ag,disableKeyboardA11y:ah,nodeExtent:ai,rfId:aj,viewport:ak,onViewportChange:al}){return eL(a),eL(b),c2(),(0,n.useRef)(!1),dE(),(0,n.useRef)(!1),c1(eF),c2(),(0,m.jsx)(d0,{onPaneClick:Q,onPaneMouseEnter:R,onPaneMouseMove:S,onPaneMouseLeave:T,onPaneContextMenu:V,onPaneScroll:U,paneClickDistance:W,deleteKeyCode:A,selectionKeyCode:u,selectionOnDrag:v,selectionMode:w,onSelectionStart:o,onSelectionEnd:p,multiSelectionKeyCode:x,panActivationKeyCode:y,zoomActivationKeyCode:z,elementsSelectable:C,zoomOnScroll:J,zoomOnPinch:K,zoomOnDoubleClick:O,panOnScroll:L,panOnScrollSpeed:M,panOnScrollMode:N,panOnDrag:P,defaultViewport:D,translateExtent:E,minZoom:F,maxZoom:G,onSelectionContextMenu:l,preventScrolling:H,noDragClassName:ae,noWheelClassName:af,noPanClassName:ag,disableKeyboardA11y:ah,onViewportChange:al,isControlledViewport:!!ak,children:(0,m.jsxs)(eE,{children:[(0,m.jsx)(eC,{edgeTypes:b,onEdgeClick:e,onEdgeDoubleClick:g,onReconnect:ab,onReconnectStart:ac,onReconnectEnd:ad,onlyRenderVisibleElements:B,onEdgeContextMenu:Y,onEdgeMouseEnter:Z,onEdgeMouseMove:$,onEdgeMouseLeave:_,reconnectRadius:aa,defaultMarkerColor:I,noPanClassName:ag,disableKeyboardA11y:ah,rfId:aj}),(0,m.jsx)(eI,{style:r,type:q,component:s,containerStyle:t}),(0,m.jsx)("div",{className:"react-flow__edgelabel-renderer"}),(0,m.jsx)(d5,{nodeTypes:a,onNodeClick:d,onNodeDoubleClick:f,onNodeMouseEnter:h,onNodeMouseMove:i,onNodeMouseLeave:j,onNodeContextMenu:k,nodeClickDistance:X,onlyRenderVisibleElements:B,noPanClassName:ag,noDragClassName:ae,disableKeyboardA11y:ah,nodeExtent:ai,rfId:aj}),(0,m.jsx)("div",{className:"react-flow__viewport-portal"})]})})}eM.displayName="GraphView";let eN=(0,n.memo)(eM),eO=({nodes:a,edges:b,defaultNodes:c,defaultEdges:d,width:f,height:g,fitView:h,fitViewOptions:i,minZoom:j=.5,maxZoom:k=2,nodeOrigin:l,nodeExtent:m}={})=>{let n=new Map,o=new Map,p=new Map,q=new Map,r=d??b??[],s=c??a??[],t=l??[0,0],u=m??bJ;cM(p,q,r);let v=cG(s,n,o,{nodeOrigin:t,nodeExtent:u,elevateNodesOnSelect:!1}),w=[0,0,1];if(h&&f&&g){let{x:a,y:b,zoom:c}=cc(bS(n,{filter:a=>!!((a.width||a.initialWidth)&&(a.height||a.initialHeight))}),f,g,j,k,i?.padding??.1);w=[a,b,c]}return{rfId:"1",width:0,height:0,transform:w,nodes:s,nodesInitialized:v,nodeLookup:n,parentLookup:o,edges:r,edgeLookup:q,connectionLookup:p,onNodesChange:null,onEdgesChange:null,hasDefaultNodes:void 0!==c,hasDefaultEdges:void 0!==d,panZoom:null,minZoom:j,maxZoom:k,translateExtent:bJ,nodeExtent:u,nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,connectionMode:e.Strict,domNode:null,paneDragging:!1,noPanClassName:"nopan",nodeOrigin:t,nodeDragThreshold:1,connectionDragThreshold:1,snapGrid:[15,15],snapToGrid:!1,nodesDraggable:!0,nodesConnectable:!0,nodesFocusable:!0,edgesFocusable:!0,edgesReconnectable:!0,elementsSelectable:!0,elevateNodesOnSelect:!0,elevateEdgesOnSelect:!1,selectNodesOnDrag:!0,multiSelectionActive:!1,fitViewQueued:h??!1,fitViewOptions:i,fitViewResolver:null,connection:{...bM},connectionClickStartHandle:null,connectOnClick:!0,ariaLiveMessage:"",autoPanOnConnect:!0,autoPanOnNodeDrag:!0,autoPanOnNodeFocus:!0,autoPanSpeed:15,connectionRadius:20,onError:b7,isValidConnection:void 0,onSelectionChangeHandlers:[],lib:"react",debug:!1,ariaLabelConfig:bL}};function eP({initialNodes:a,initialEdges:b,defaultNodes:c,defaultEdges:d,initialWidth:e,initialHeight:f,initialMinZoom:g,initialMaxZoom:h,initialFitViewOptions:i,fitView:j,nodeOrigin:k,nodeExtent:l,children:o}){let[p]=(0,n.useState)(()=>(({nodes:a,edges:b,defaultNodes:c,defaultEdges:d,width:e,height:f,fitView:g,fitViewOptions:h,minZoom:i,maxZoom:j,nodeOrigin:k,nodeExtent:l})=>{let m,n;return m=(m,n)=>{async function o(){let{nodeLookup:a,panZoom:b,fitViewOptions:c,fitViewResolver:d,width:e,height:f,minZoom:g,maxZoom:h}=n();b&&(await bU({nodes:a,width:e,height:f,panZoom:b,minZoom:g,maxZoom:h},c),d?.resolve(!0),m({fitViewResolver:null}))}return{...eO({nodes:a,edges:b,width:e,height:f,fitView:g,fitViewOptions:h,minZoom:i,maxZoom:j,nodeOrigin:k,nodeExtent:l,defaultNodes:c,defaultEdges:d}),setNodes:a=>{let{nodeLookup:b,parentLookup:c,nodeOrigin:d,elevateNodesOnSelect:e,fitViewQueued:f}=n(),g=cG(a,b,c,{nodeOrigin:d,nodeExtent:l,elevateNodesOnSelect:e,checkEquality:!0});f&&g?(o(),m({nodes:a,nodesInitialized:g,fitViewQueued:!1,fitViewOptions:void 0})):m({nodes:a,nodesInitialized:g})},setEdges:a=>{let{connectionLookup:b,edgeLookup:c}=n();cM(b,c,a),m({edges:a})},setDefaultNodesAndEdges:(a,b)=>{if(a){let{setNodes:b}=n();b(a),m({hasDefaultNodes:!0})}if(b){let{setEdges:a}=n();a(b),m({hasDefaultEdges:!0})}},updateNodeInternals:a=>{let{triggerNodeChanges:b,nodeLookup:c,parentLookup:d,domNode:e,nodeOrigin:f,nodeExtent:g,debug:h,fitViewQueued:i}=n(),{changes:j,updatedInternals:k}=function(a,b,c,d,e,f){let g=d?.querySelector(".xyflow__viewport"),h=!1;if(!g)return{changes:[],updatedInternals:h};let i=[],j=window.getComputedStyle(g),{m22:k}=new window.DOMMatrixReadOnly(j.transform),l=[];for(let d of a.values()){let a=b.get(d.id);if(!a)continue;if(a.hidden){b.set(a.id,{...a,internals:{...a.internals,handleBounds:void 0}}),h=!0;continue}let g=ci(d.nodeElement),j=a.measured.width!==g.width||a.measured.height!==g.height;if(g.width&&g.height&&(j||!a.internals.handleBounds||d.force)){let m=d.nodeElement.getBoundingClientRect(),n=ce(a.extent)?a.extent:f,{positionAbsolute:o}=a.internals;a.parentId&&"parent"===a.extent?o=bY(o,g,b.get(a.parentId)):n&&(o=bX(o,n,g));let p={...a,measured:g,internals:{...a.internals,positionAbsolute:o,handleBounds:{source:cm("source",d.nodeElement,m,k,a.id),target:cm("target",d.nodeElement,m,k,a.id)}}};b.set(a.id,p),a.parentId&&cH(p,b,c,{nodeOrigin:e}),h=!0,j&&(i.push({id:a.id,type:"dimensions",dimensions:g}),a.expandParent&&a.parentId&&l.push({id:a.id,parentId:a.parentId,rect:b1(p,e)}))}}if(l.length>0){let a=cJ(l,b,c,e);i.push(...a)}return{changes:i,updatedInternals:h}}(a,c,d,e,f,g);k&&(!function(a,b,c){let d=cF(cD,c);for(let c of a.values())if(c.parentId)cH(c,a,b,d);else{let a=bX(bR(c,d.nodeOrigin),ce(c.extent)?c.extent:d.nodeExtent,cf(c));c.internals.positionAbsolute=a}}(c,d,{nodeOrigin:f,nodeExtent:g}),i?(o(),m({fitViewQueued:!1,fitViewOptions:void 0})):m({}),j?.length>0&&(h&&console.log("React Flow: trigger node changes",j),b?.(j)))},updateNodePositions:(a,b=!1)=>{let c=[],d=[],{nodeLookup:e,triggerNodeChanges:f}=n();for(let[f,g]of a){let a=e.get(f),h=!!(a?.expandParent&&a?.parentId&&g?.position),i={id:f,type:"position",position:h?{x:Math.max(0,g.position.x),y:Math.max(0,g.position.y)}:g.position,dragging:b};h&&a.parentId&&c.push({id:f,parentId:a.parentId,rect:{...g.internals.positionAbsolute,width:g.measured.width??0,height:g.measured.height??0}}),d.push(i)}if(c.length>0){let{parentLookup:a,nodeOrigin:b}=n(),f=cJ(c,e,a,b);d.push(...f)}f(d)},triggerNodeChanges:a=>{let{onNodesChange:b,setNodes:c,nodes:d,hasDefaultNodes:e,debug:f}=n();a?.length&&(e&&c(ds(a,d)),f&&console.log("React Flow: trigger node changes",a),b?.(a))},triggerEdgeChanges:a=>{let{onEdgesChange:b,setEdges:c,edges:d,hasDefaultEdges:e,debug:f}=n();a?.length&&(e&&c(ds(a,d)),f&&console.log("React Flow: trigger edge changes",a),b?.(a))},addSelectedNodes:a=>{let{multiSelectionActive:b,edgeLookup:c,nodeLookup:d,triggerNodeChanges:e,triggerEdgeChanges:f}=n();if(b)return void e(a.map(a=>dt(a,!0)));e(du(d,new Set([...a]),!0)),f(du(c))},addSelectedEdges:a=>{let{multiSelectionActive:b,edgeLookup:c,nodeLookup:d,triggerNodeChanges:e,triggerEdgeChanges:f}=n();if(b)return void f(a.map(a=>dt(a,!0)));f(du(c,new Set([...a]))),e(du(d,new Set,!0))},unselectNodesAndEdges:({nodes:a,edges:b}={})=>{let{edges:c,nodes:d,nodeLookup:e,triggerNodeChanges:f,triggerEdgeChanges:g}=n(),h=(a||d).map(a=>{let b=e.get(a.id);return b&&(b.selected=!1),dt(a.id,!1)}),i=(b||c).map(a=>dt(a.id,!1));f(h),g(i)},setMinZoom:a=>{let{panZoom:b,maxZoom:c}=n();b?.setScaleExtent([a,c]),m({minZoom:a})},setMaxZoom:a=>{let{panZoom:b,minZoom:c}=n();b?.setScaleExtent([c,a]),m({maxZoom:a})},setTranslateExtent:a=>{n().panZoom?.setTranslateExtent(a),m({translateExtent:a})},setPaneClickDistance:a=>{n().panZoom?.setClickDistance(a)},resetSelectedElements:()=>{let{edges:a,nodes:b,triggerNodeChanges:c,triggerEdgeChanges:d,elementsSelectable:e}=n();if(!e)return;let f=b.reduce((a,b)=>b.selected?[...a,dt(b.id,!1)]:a,[]),g=a.reduce((a,b)=>b.selected?[...a,dt(b.id,!1)]:a,[]);c(f),d(g)},setNodeExtent:a=>{let{nodes:b,nodeLookup:c,parentLookup:d,nodeOrigin:e,elevateNodesOnSelect:f,nodeExtent:g}=n();(a[0][0]!==g[0][0]||a[0][1]!==g[0][1]||a[1][0]!==g[1][0]||a[1][1]!==g[1][1])&&(cG(b,c,d,{nodeOrigin:e,nodeExtent:a,elevateNodesOnSelect:f,checkEquality:!1}),m({nodeExtent:a}))},panBy:a=>{let{transform:b,width:c,height:d,panZoom:e,translateExtent:f}=n();return cK({delta:a,panZoom:e,transform:b,translateExtent:f,width:c,height:d})},setCenter:async(a,b,c)=>{let{width:d,height:e,maxZoom:f,panZoom:g}=n();if(!g)return Promise.resolve(!1);let h=void 0!==c?.zoom?c.zoom:f;return await g.setViewport({x:d/2-a*h,y:e/2-b*h,zoom:h},{duration:c?.duration,ease:c?.ease,interpolate:c?.interpolate}),Promise.resolve(!0)},cancelConnection:()=>{m({connection:{...bM}})},updateConnection:a=>{m({connection:a})},reset:()=>m({...eO()})}},n=Object.is,m?cY(m,n):cY})({nodes:a,edges:b,defaultNodes:c,defaultEdges:d,width:e,height:f,fitView:j,minZoom:g,maxZoom:h,fitViewOptions:i,nodeOrigin:k,nodeExtent:l}));return(0,m.jsx)(c_,{value:p,children:(0,m.jsx)(dC,{children:o})})}function eQ({children:a,nodes:b,edges:c,defaultNodes:d,defaultEdges:e,width:f,height:g,fitView:h,fitViewOptions:i,minZoom:j,maxZoom:k,nodeOrigin:l,nodeExtent:o}){return(0,n.useContext)(c$)?(0,m.jsx)(m.Fragment,{children:a}):(0,m.jsx)(eP,{initialNodes:b,initialEdges:c,defaultNodes:d,defaultEdges:e,initialWidth:f,initialHeight:g,fitView:h,initialFitViewOptions:i,initialMinZoom:j,initialMaxZoom:k,nodeOrigin:l,nodeExtent:o,children:a})}let eR={width:"100%",height:"100%",overflow:"hidden",position:"relative",zIndex:0};var eS=dy(function({nodes:a,edges:b,defaultNodes:c,defaultEdges:d,className:e,nodeTypes:i,edgeTypes:j,onNodeClick:k,onEdgeClick:l,onInit:o,onMove:p,onMoveStart:q,onMoveEnd:r,onConnect:s,onConnectStart:t,onConnectEnd:u,onClickConnectStart:v,onClickConnectEnd:w,onNodeMouseEnter:x,onNodeMouseMove:y,onNodeMouseLeave:A,onNodeContextMenu:B,onNodeDoubleClick:C,onNodeDragStart:D,onNodeDrag:E,onNodeDragStop:F,onNodesDelete:G,onEdgesDelete:H,onDelete:I,onSelectionChange:J,onSelectionDragStart:K,onSelectionDrag:L,onSelectionDragStop:M,onSelectionContextMenu:N,onSelectionStart:O,onSelectionEnd:P,onBeforeDelete:Q,connectionMode:R,connectionLineType:S=h.Bezier,connectionLineStyle:T,connectionLineComponent:U,connectionLineContainerStyle:V,deleteKeyCode:W="Backspace",selectionKeyCode:X="Shift",selectionOnDrag:Y=!1,selectionMode:Z=g.Full,panActivationKeyCode:$="Space",multiSelectionKeyCode:_=cd()?"Meta":"Control",zoomActivationKeyCode:aa=cd()?"Meta":"Control",snapToGrid:ab,snapGrid:ac,onlyRenderVisibleElements:ad=!1,selectNodesOnDrag:ae,nodesDraggable:af,autoPanOnNodeFocus:ag,nodesConnectable:ah,nodesFocusable:ai,nodeOrigin:aj=dj,edgesFocusable:ak,edgesReconnectable:al,elementsSelectable:am=!0,defaultViewport:an=dk,minZoom:ao=.5,maxZoom:ap=2,translateExtent:aq=bJ,preventScrolling:ar=!0,nodeExtent:as,defaultMarkerColor:at="#b1b1b7",zoomOnScroll:au=!0,zoomOnPinch:av=!0,panOnScroll:aw=!1,panOnScrollSpeed:ax=.5,panOnScrollMode:ay=f.Free,zoomOnDoubleClick:az=!0,panOnDrag:aA=!0,onPaneClick:aB,onPaneMouseEnter:aC,onPaneMouseMove:aD,onPaneMouseLeave:aE,onPaneScroll:aF,onPaneContextMenu:aG,paneClickDistance:aH=0,nodeClickDistance:aI=0,children:aJ,onReconnect:aK,onReconnectStart:aL,onReconnectEnd:aM,onEdgeContextMenu:aN,onEdgeDoubleClick:aO,onEdgeMouseEnter:aP,onEdgeMouseMove:aQ,onEdgeMouseLeave:aR,reconnectRadius:aS=10,onNodesChange:aT,onEdgesChange:aU,noDragClassName:aV="nodrag",noWheelClassName:aW="nowheel",noPanClassName:aX="nopan",fitView:aY,fitViewOptions:aZ,connectOnClick:a$,attributionPosition:a_,proOptions:a0,defaultEdgeOptions:a1,elevateNodesOnSelect:a2,elevateEdgesOnSelect:a3,disableKeyboardA11y:a4=!1,autoPanOnConnect:a5,autoPanOnNodeDrag:a6,autoPanSpeed:a7,connectionRadius:a8,isValidConnection:a9,onError:ba,style:bb,id:bc,nodeDragThreshold:bd,connectionDragThreshold:be,viewport:bf,onViewportChange:bg,width:bh,height:bi,colorMode:bj="light",debug:bk,onScroll:bl,ariaLabelConfig:bm,...bn},bo){let bp=bc||"1",bq=function(a){let[b,c]=(0,n.useState)("system"===a?null:a);return null!==b?b:"light"}(bj),br=(0,n.useCallback)(a=>{a.currentTarget.scrollTo({top:0,left:0,behavior:"instant"}),bl?.(a)},[bl]);return(0,m.jsx)("div",{"data-testid":"rf__wrapper",...bn,onScroll:br,style:{...bb,...eR},ref:bo,className:z(["react-flow",e,bq]),id:bc,role:"application",children:(0,m.jsxs)(eQ,{nodes:a,edges:b,width:bh,height:bi,fitView:aY,fitViewOptions:aZ,minZoom:ao,maxZoom:ap,nodeOrigin:aj,nodeExtent:as,children:[(0,m.jsx)(eN,{onInit:o,onNodeClick:k,onEdgeClick:l,onNodeMouseEnter:x,onNodeMouseMove:y,onNodeMouseLeave:A,onNodeContextMenu:B,onNodeDoubleClick:C,nodeTypes:i,edgeTypes:j,connectionLineType:S,connectionLineStyle:T,connectionLineComponent:U,connectionLineContainerStyle:V,selectionKeyCode:X,selectionOnDrag:Y,selectionMode:Z,deleteKeyCode:W,multiSelectionKeyCode:_,panActivationKeyCode:$,zoomActivationKeyCode:aa,onlyRenderVisibleElements:ad,defaultViewport:an,translateExtent:aq,minZoom:ao,maxZoom:ap,preventScrolling:ar,zoomOnScroll:au,zoomOnPinch:av,zoomOnDoubleClick:az,panOnScroll:aw,panOnScrollSpeed:ax,panOnScrollMode:ay,panOnDrag:aA,onPaneClick:aB,onPaneMouseEnter:aC,onPaneMouseMove:aD,onPaneMouseLeave:aE,onPaneScroll:aF,onPaneContextMenu:aG,paneClickDistance:aH,nodeClickDistance:aI,onSelectionContextMenu:N,onSelectionStart:O,onSelectionEnd:P,onReconnect:aK,onReconnectStart:aL,onReconnectEnd:aM,onEdgeContextMenu:aN,onEdgeDoubleClick:aO,onEdgeMouseEnter:aP,onEdgeMouseMove:aQ,onEdgeMouseLeave:aR,reconnectRadius:aS,defaultMarkerColor:at,noDragClassName:aV,noWheelClassName:aW,noPanClassName:aX,rfId:bp,disableKeyboardA11y:a4,nodeExtent:as,viewport:bf,onViewportChange:bg}),(0,m.jsx)(dp,{nodes:a,edges:b,defaultNodes:c,defaultEdges:d,onConnect:s,onConnectStart:t,onConnectEnd:u,onClickConnectStart:v,onClickConnectEnd:w,nodesDraggable:af,autoPanOnNodeFocus:ag,nodesConnectable:ah,nodesFocusable:ai,edgesFocusable:ak,edgesReconnectable:al,elementsSelectable:am,elevateNodesOnSelect:a2,elevateEdgesOnSelect:a3,minZoom:ao,maxZoom:ap,nodeExtent:as,onNodesChange:aT,onEdgesChange:aU,snapToGrid:ab,snapGrid:ac,connectionMode:R,translateExtent:aq,connectOnClick:a$,defaultEdgeOptions:a1,fitView:aY,fitViewOptions:aZ,onNodesDelete:G,onEdgesDelete:H,onDelete:I,onNodeDragStart:D,onNodeDrag:E,onNodeDragStop:F,onSelectionDrag:L,onSelectionDragStart:K,onSelectionDragStop:M,onMove:p,onMoveStart:q,onMoveEnd:r,noPanClassName:aX,nodeOrigin:aj,rfId:bp,autoPanOnConnect:a5,autoPanOnNodeDrag:a6,autoPanSpeed:a7,onError:ba,connectionRadius:a8,isValidConnection:a9,selectNodesOnDrag:ae,nodeDragThreshold:bd,connectionDragThreshold:be,onBeforeDelete:Q,paneClickDistance:aH,debug:bk,ariaLabelConfig:bm}),(0,m.jsx)(di,{onSelectionChange:J}),aJ,(0,m.jsx)(dc,{proOptions:a0,position:a_}),(0,m.jsx)(da,{rfId:bp,disableKeyboardA11y:a4})]})})});function eT({dimensions:a,lineWidth:b,variant:c,className:d}){return(0,m.jsx)("path",{strokeWidth:b,d:`M${a[0]/2} 0 V${a[1]} M0 ${a[1]/2} H${a[0]}`,className:z(["react-flow__background-pattern",c,d])})}function eU({radius:a,className:b}){return(0,m.jsx)("circle",{cx:a,cy:a,r:a,className:z(["react-flow__background-pattern","dots",b])})}bI.error014(),function(a){a.Lines="lines",a.Dots="dots",a.Cross="cross"}(l||(l={}));let eV={[l.Dots]:1,[l.Lines]:1,[l.Cross]:6},eW=a=>({transform:a.transform,patternId:`pattern-${a.rfId}`});function eX({id:a,variant:b=l.Dots,gap:c=20,size:d,lineWidth:e=1,offset:f=0,color:g,bgColor:h,style:i,className:j,patternClassName:k}){let o=(0,n.useRef)(null),{transform:p,patternId:q}=c1(eW,cZ),r=d||eV[b],s=b===l.Dots,t=b===l.Cross,u=Array.isArray(c)?c:[c,c],v=[u[0]*p[2]||1,u[1]*p[2]||1],w=r*p[2],x=Array.isArray(f)?f:[f,f],y=t?[w,w]:v,A=[x[0]*p[2]||1+y[0]/2,x[1]*p[2]||1+y[1]/2],B=`${q}${a||""}`;return(0,m.jsxs)("svg",{className:z(["react-flow__background",j]),style:{...i,...dG,"--xy-background-color-props":h,"--xy-background-pattern-color-props":g},ref:o,"data-testid":"rf__background",children:[(0,m.jsx)("pattern",{id:B,x:p[0]%v[0],y:p[1]%v[1],width:v[0],height:v[1],patternUnits:"userSpaceOnUse",patternTransform:`translate(-${A[0]},-${A[1]})`,children:s?(0,m.jsx)(eU,{radius:w/2,className:k}):(0,m.jsx)(eT,{dimensions:y,lineWidth:e,variant:b,className:k})}),(0,m.jsx)("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:`url(#${B})`})]})}eX.displayName="Background";let eY=(0,n.memo)(eX);function eZ(){return(0,m.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",children:(0,m.jsx)("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"})})}function e$(){return(0,m.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5",children:(0,m.jsx)("path",{d:"M0 0h32v4.2H0z"})})}function e_(){return(0,m.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30",children:(0,m.jsx)("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"})})}function e0(){return(0,m.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32",children:(0,m.jsx)("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"})})}function e1(){return(0,m.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32",children:(0,m.jsx)("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"})})}function e2({children:a,className:b,...c}){return(0,m.jsx)("button",{type:"button",className:z(["react-flow__controls-button",b]),...c,children:a})}let e3=a=>({isInteractive:a.nodesDraggable||a.nodesConnectable||a.elementsSelectable,minZoomReached:a.transform[2]<=a.minZoom,maxZoomReached:a.transform[2]>=a.maxZoom,ariaLabelConfig:a.ariaLabelConfig});function e4({style:a,showZoom:b=!0,showFitView:c=!0,showInteractive:d=!0,fitViewOptions:e,onZoomIn:f,onZoomOut:g,onFitView:h,onInteractiveChange:i,className:j,children:k,position:l="bottom-left",orientation:n="vertical","aria-label":o}){let p=c2(),{isInteractive:q,minZoomReached:r,maxZoomReached:s,ariaLabelConfig:t}=c1(e3,cZ),{zoomIn:u,zoomOut:v,fitView:w}=dE();return(0,m.jsxs)(db,{className:z(["react-flow__controls","horizontal"===n?"horizontal":"vertical",j]),position:l,style:a,"data-testid":"rf__controls","aria-label":o??t["controls.ariaLabel"],children:[b&&(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(e2,{onClick:()=>{u(),f?.()},className:"react-flow__controls-zoomin",title:t["controls.zoomIn.ariaLabel"],"aria-label":t["controls.zoomIn.ariaLabel"],disabled:s,children:(0,m.jsx)(eZ,{})}),(0,m.jsx)(e2,{onClick:()=>{v(),g?.()},className:"react-flow__controls-zoomout",title:t["controls.zoomOut.ariaLabel"],"aria-label":t["controls.zoomOut.ariaLabel"],disabled:r,children:(0,m.jsx)(e$,{})})]}),c&&(0,m.jsx)(e2,{className:"react-flow__controls-fitview",onClick:()=>{w(e),h?.()},title:t["controls.fitView.ariaLabel"],"aria-label":t["controls.fitView.ariaLabel"],children:(0,m.jsx)(e_,{})}),d&&(0,m.jsx)(e2,{className:"react-flow__controls-interactive",onClick:()=>{p.setState({nodesDraggable:!q,nodesConnectable:!q,elementsSelectable:!q}),i?.(!q)},title:t["controls.interactive.ariaLabel"],"aria-label":t["controls.interactive.ariaLabel"],children:q?(0,m.jsx)(e1,{}):(0,m.jsx)(e0,{})}),k]})}e4.displayName="Controls";let e5=(0,n.memo)(e4),e6=(0,n.memo)(function({id:a,x:b,y:c,width:d,height:e,style:f,color:g,strokeColor:h,strokeWidth:i,className:j,borderRadius:k,shapeRendering:l,selected:n,onClick:o}){let{background:p,backgroundColor:q}=f||{};return(0,m.jsx)("rect",{className:z(["react-flow__minimap-node",{selected:n},j]),x:b,y:c,rx:k,ry:k,width:d,height:e,style:{fill:g||p||q,stroke:h,strokeWidth:i},shapeRendering:l,onClick:o?b=>o(b,a):void 0})}),e7=a=>a.nodes.map(a=>a.id),e8=a=>a instanceof Function?a:()=>a,e9=(0,n.memo)(function({id:a,nodeColorFunc:b,nodeStrokeColorFunc:c,nodeClassNameFunc:d,nodeBorderRadius:e,nodeStrokeWidth:f,shapeRendering:g,NodeComponent:h,onClick:i}){let{node:j,x:k,y:l,width:n,height:o}=c1(b=>{let{internals:c}=b.nodeLookup.get(a),d=c.userNode,{x:e,y:f}=c.positionAbsolute,{width:g,height:h}=cf(d);return{node:d,x:e,y:f,width:g,height:h}},cZ);return j&&!j.hidden&&cg(j)?(0,m.jsx)(h,{x:k,y:l,width:n,height:o,style:j.style,selected:!!j.selected,className:d(j),color:b(j),borderRadius:e,strokeColor:c(j),strokeWidth:f,shapeRendering:g,onClick:i,id:j.id}):null});var fa=(0,n.memo)(function({nodeStrokeColor:a,nodeColor:b,nodeClassName:c="",nodeBorderRadius:d=5,nodeStrokeWidth:e,nodeComponent:f=e6,onClick:g}){let h=c1(e7,cZ),i=e8(b),j=e8(a),k=e8(c);return(0,m.jsx)(m.Fragment,{children:h.map(a=>(0,m.jsx)(e9,{id:a,nodeColorFunc:i,nodeStrokeColorFunc:j,nodeClassNameFunc:k,nodeBorderRadius:d,nodeStrokeWidth:e,NodeComponent:f,onClick:g,shapeRendering:"crispEdges"},a))})});let fb=a=>!a.hidden,fc=a=>{let b={x:-a.transform[0]/a.transform[2],y:-a.transform[1]/a.transform[2],width:a.width/a.transform[2],height:a.height/a.transform[2]};return{viewBB:b,boundingRect:a.nodeLookup.size>0?b3(bS(a.nodeLookup,{filter:fb}),b):b,rfId:a.rfId,panZoom:a.panZoom,translateExtent:a.translateExtent,flowWidth:a.width,flowHeight:a.height,ariaLabelConfig:a.ariaLabelConfig}};function fd({style:a,className:b,nodeStrokeColor:c,nodeColor:d,nodeClassName:e="",nodeBorderRadius:f=5,nodeStrokeWidth:g,nodeComponent:h,bgColor:i,maskColor:j,maskStrokeColor:k,maskStrokeWidth:l,position:o="bottom-right",onClick:p,onNodeClick:q,pannable:r=!1,zoomable:s=!1,ariaLabel:t,inversePan:u,zoomStep:v=10,offsetScale:w=5}){let x=c2(),y=(0,n.useRef)(null),{boundingRect:A,viewBB:B,rfId:C,panZoom:D,translateExtent:E,flowWidth:F,flowHeight:G,ariaLabelConfig:H}=c1(fc,cZ),I=a?.width??200,J=a?.height??150,K=Math.max(A.width/I,A.height/J),L=K*I,M=K*J,N=w*K,O=A.x-(L-A.width)/2-N,P=A.y-(M-A.height)/2-N,Q=L+2*N,R=M+2*N,S=`react-flow__minimap-desc-${C}`,T=(0,n.useRef)(0),U=(0,n.useRef)();T.current=K;let V=p?a=>{let[b,c]=U.current?.pointer(a)||[0,0];p(a,{x:b,y:c})}:void 0,W=q?(0,n.useCallback)((a,b)=>{q(a,x.getState().nodeLookup.get(b).internals.userNode)},[]):void 0,X=t??H["minimap.ariaLabel"];return(0,m.jsx)(db,{position:o,style:{...a,"--xy-minimap-background-color-props":"string"==typeof i?i:void 0,"--xy-minimap-mask-background-color-props":"string"==typeof j?j:void 0,"--xy-minimap-mask-stroke-color-props":"string"==typeof k?k:void 0,"--xy-minimap-mask-stroke-width-props":"number"==typeof l?l*K:void 0,"--xy-minimap-node-background-color-props":"string"==typeof d?d:void 0,"--xy-minimap-node-stroke-color-props":"string"==typeof c?c:void 0,"--xy-minimap-node-stroke-width-props":"number"==typeof g?g:void 0},className:z(["react-flow__minimap",b]),"data-testid":"rf__minimap",children:(0,m.jsxs)("svg",{width:I,height:J,viewBox:`${O} ${P} ${Q} ${R}`,className:"react-flow__minimap-svg",role:"img","aria-labelledby":S,ref:y,onClick:V,children:[X&&(0,m.jsx)("title",{id:S,children:X}),(0,m.jsx)(fa,{onClick:W,nodeColor:d,nodeStrokeColor:c,nodeBorderRadius:f,nodeClassName:e,nodeStrokeWidth:g,nodeComponent:h}),(0,m.jsx)("path",{className:"react-flow__minimap-mask",d:`M${O-N},${P-N}h${Q+2*N}v${R+2*N}h${-Q-2*N}z
        M${B.x},${B.y}h${B.width}v${B.height}h${-B.width}z`,fillRule:"evenodd",pointerEvents:"none"})]})})}fd.displayName="MiniMap";let fe=(0,n.memo)(fd);k.Line,k.Handle,c(1763);let ff=v("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]]),fg=v("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]),fh=v("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),fi=v("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),fj=v("git-branch",[["line",{x1:"6",x2:"6",y1:"3",y2:"15",key:"17qcm7"}],["circle",{cx:"18",cy:"6",r:"3",key:"1h7g24"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["path",{d:"M18 9a9 9 0 0 1-9 9",key:"n2h4wq"}]]),fk=(0,n.memo)(({data:a,selected:b})=>{let{fileNode:c,isSelected:d,onSelect:e}=a,f=(()=>{switch(c.name.split(".").pop()?.toLowerCase()){case"js":case"jsx":case"ts":case"tsx":case"py":case"java":case"cpp":case"c":case"cs":case"php":case"rb":case"go":case"rs":case"swift":case"kt":return ff;case"png":case"jpg":case"jpeg":case"gif":case"svg":case"webp":return fg;case"json":case"xml":case"yaml":case"yml":case"toml":case"ini":return fh;default:return fi}})(),g={javascript:"border-yellow-400 bg-yellow-50",typescript:"border-blue-400 bg-blue-50",python:"border-green-400 bg-green-50",java:"border-red-400 bg-red-50",cpp:"border-purple-400 bg-purple-50",c:"border-gray-400 bg-gray-50",csharp:"border-indigo-400 bg-indigo-50",php:"border-purple-500 bg-purple-50",ruby:"border-red-500 bg-red-50",go:"border-cyan-400 bg-cyan-50",rust:"border-orange-400 bg-orange-50",swift:"border-orange-500 bg-orange-50",kotlin:"border-purple-600 bg-purple-50",html:"border-orange-500 bg-orange-50",css:"border-blue-500 bg-blue-50",scss:"border-pink-500 bg-pink-50",json:"border-yellow-500 bg-yellow-50",markdown:"border-gray-600 bg-gray-50"}[c.language||""]||"border-gray-300 bg-white",h=c.dependencies&&c.dependencies.length>0;return(0,m.jsxs)("div",{className:o("px-4 py-3 shadow-md rounded-lg border-2 cursor-pointer transition-all duration-200 min-w-[180px] max-w-[220px]",g,d||b?"ring-2 ring-blue-500 ring-offset-2 shadow-lg scale-105":"hover:shadow-lg hover:scale-102"),onClick:()=>e(c.id),children:[(0,m.jsx)(dU,{type:"target",position:j.Top,className:"w-3 h-3 !bg-blue-500 !border-2 !border-white"}),(0,m.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,m.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:(0,m.jsx)(f,{className:"w-5 h-5 text-gray-600"})}),(0,m.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,m.jsx)("div",{className:"text-sm font-medium text-gray-900 truncate",children:c.name}),c.language&&(0,m.jsx)("div",{className:"text-xs text-gray-600 mt-1",children:c.language}),c.size&&(0,m.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[(c.size/1024).toFixed(1)," KB"]}),h&&(0,m.jsxs)("div",{className:"flex items-center space-x-1 mt-2",children:[(0,m.jsx)(fj,{className:"w-3 h-3 text-blue-600"}),(0,m.jsxs)("span",{className:"text-xs text-blue-600 font-medium",children:[c.dependencies.length," bağımlılık"]})]})]})]}),(0,m.jsx)(dU,{type:"source",position:j.Bottom,className:"w-3 h-3 !bg-green-500 !border-2 !border-white"})]})});fk.displayName="FileNodeComponent";let fl=v("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),fm=v("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),fn=v("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function fo({fileNode:a,onClose:b,onSave:c}){let[d,e]=(0,n.useState)(a.content||""),[f,g]=(0,n.useState)(!1);return(0,m.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,m.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl h-full max-h-[90vh] flex flex-col",children:[(0,m.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700",children:[(0,m.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,m.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:a.name}),f&&(0,m.jsx)("span",{className:"text-xs bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200 px-2 py-1 rounded",children:"Değiştirildi"})]}),(0,m.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,m.jsxs)(p,{variant:"outline",size:"sm",onClick:()=>{let b=new Blob([d],{type:"text/plain"}),c=URL.createObjectURL(b),e=document.createElement("a");e.href=c,e.download=a.name,document.body.appendChild(e),e.click(),document.body.removeChild(e),URL.revokeObjectURL(c)},className:"flex items-center space-x-1",children:[(0,m.jsx)(fl,{className:"w-4 h-4"}),(0,m.jsx)("span",{children:"İndir"})]}),c&&(0,m.jsxs)(p,{variant:"default",size:"sm",onClick:()=>{c&&c(d),g(!1)},disabled:!f,className:"flex items-center space-x-1",children:[(0,m.jsx)(fm,{className:"w-4 h-4"}),(0,m.jsx)("span",{children:"Kaydet"})]}),(0,m.jsx)(p,{variant:"ghost",size:"sm",onClick:b,className:"flex items-center space-x-1",children:(0,m.jsx)(fn,{className:"w-4 h-4"})})]})]}),(0,m.jsx)("div",{className:"px-4 py-2 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700",children:(0,m.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400",children:[(0,m.jsxs)("span",{children:["Yol: ",a.path]}),a.language&&(0,m.jsxs)("span",{children:["Dil: ",a.language]}),a.size&&(0,m.jsxs)("span",{children:["Boyut: ",(a.size/1024).toFixed(1)," KB"]}),(0,m.jsxs)("span",{children:["Satırlar: ",d.split("\n").length]})]})}),(0,m.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,m.jsx)("textarea",{value:d,onChange:b=>{var c;e(c=b.target.value),g(c!==a.content)},className:o("w-full h-full p-4 font-mono text-sm resize-none border-none outline-none","bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100","focus:ring-0 focus:border-transparent",{javascript:"language-javascript",typescript:"language-typescript",python:"language-python",java:"language-java",cpp:"language-cpp",c:"language-c",csharp:"language-csharp",php:"language-php",ruby:"language-ruby",go:"language-go",rust:"language-rust",html:"language-html",css:"language-css",json:"language-json",markdown:"language-markdown"}[a.language||""]||"language-text"),placeholder:a.content?"":"Dosya i\xe7eriği y\xfcklenemedi...",readOnly:!a.content,spellCheck:!1,style:{lineHeight:"1.5",tabSize:2}})}),(0,m.jsx)("div",{className:"px-4 py-2 bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700",children:(0,m.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400",children:[(0,m.jsx)("div",{className:"flex items-center space-x-4",children:a.dependencies&&a.dependencies.length>0&&(0,m.jsxs)("span",{children:[a.dependencies.length," bağımlılık"]})}),(0,m.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,m.jsx)("span",{children:"UTF-8"}),(0,m.jsx)("span",{children:"LF"}),a.language&&(0,m.jsx)("span",{children:a.language.toUpperCase()})]})]})})]})})}let fp={fileNode:fk};function fq({analysisResult:a}){let[b,c]=(0,n.useState)(null),[d,e]=(0,n.useState)(null),f=(0,n.useMemo)(()=>{let b=[],{graph:d}=a,e=d.nodes.filter(a=>"file"===a.type);return e.forEach((a,d)=>{let f=Math.ceil(Math.sqrt(e.length)),g=Math.floor(d/f);b.push({id:a.id,type:"fileNode",position:{x:d%f*250+50,y:150*g+50},data:{fileNode:a,isSelected:!1,onSelect:a=>c(a)},draggable:!0})}),b},[a]),g=(0,n.useMemo)(()=>a.graph.edges.map(a=>({id:a.id,source:a.source,target:a.target,type:"smoothstep",animated:!0,label:a.label,style:{stroke:"#6366f1",strokeWidth:2},labelStyle:{fontSize:10,fontWeight:600},labelBgStyle:{fill:"#ffffff",fillOpacity:.8}})),[a]),[h,,i]=function(a){let[b,c]=(0,n.useState)(a),d=(0,n.useCallback)(a=>c(b=>ds(a,b)),[]);return[b,c,d]}(f),[j,k,o]=function(a){let[b,c]=(0,n.useState)(a),d=(0,n.useCallback)(a=>c(b=>ds(a,b)),[]);return[b,c,d]}(g),p=(0,n.useCallback)(a=>k(b=>cs(a,b)),[k]),q=(0,n.useMemo)(()=>h.map(a=>({...a,data:{...a.data,isSelected:a.id===b}})),[h,b]),r=a=>({javascript:"#f7df1e",typescript:"#3178c6",python:"#3776ab",java:"#ed8b00",cpp:"#00599c",c:"#a8b9cc",csharp:"#239120",php:"#777bb4",ruby:"#cc342d",go:"#00add8",rust:"#dea584",swift:"#fa7343",kotlin:"#7f52ff",html:"#e34f26",css:"#1572b6",scss:"#cf649a",json:"#000000",markdown:"#083fa1"})[a||""]||"#6b7280";return(0,m.jsxs)("div",{className:"h-[600px] w-full",children:[(0,m.jsxs)(eS,{nodes:q,edges:j,onNodesChange:i,onEdgesChange:o,onConnect:p,nodeTypes:fp,fitView:!0,attributionPosition:"bottom-left",children:[(0,m.jsx)(e5,{}),(0,m.jsx)(fe,{nodeColor:a=>{let b=a.data?.fileNode;return r(b?.language)},nodeStrokeWidth:3,zoomable:!0,pannable:!0}),(0,m.jsx)(eY,{variant:l.Dots,gap:20,size:1,color:"#e5e7eb"})]}),b&&(0,m.jsx)("div",{className:"absolute top-4 right-4 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border p-4 max-h-96 overflow-y-auto",children:(()=>{let d=a.graph.nodes.find(a=>a.id===b);return d?(0,m.jsxs)("div",{className:"space-y-3",children:[(0,m.jsxs)("div",{children:[(0,m.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:d.name}),(0,m.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:d.path})]}),d.language&&(0,m.jsxs)("div",{children:[(0,m.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Dil:"}),(0,m.jsx)("span",{className:"ml-2 text-sm px-2 py-1 rounded text-white",style:{backgroundColor:r(d.language)},children:d.language})]}),d.size&&(0,m.jsxs)("div",{children:[(0,m.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Boyut:"}),(0,m.jsxs)("span",{className:"ml-2 text-sm text-gray-600 dark:text-gray-400",children:[(d.size/1024).toFixed(1)," KB"]})]}),d.dependencies&&d.dependencies.length>0&&(0,m.jsxs)("div",{children:[(0,m.jsxs)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:["Bağımlılıklar (",d.dependencies.length,")"]}),(0,m.jsx)("div",{className:"space-y-1 max-h-32 overflow-y-auto",children:d.dependencies.map((a,b)=>(0,m.jsx)("div",{className:"text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded",children:a},b))})]}),(0,m.jsxs)("div",{className:"flex space-x-2 pt-2 border-t border-gray-200 dark:border-gray-700",children:[d.content&&(0,m.jsx)("button",{onClick:()=>e(d),className:"flex-1 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 py-2",children:"D\xfczenle"}),(0,m.jsx)("button",{onClick:()=>c(null),className:"flex-1 text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 py-2",children:"Kapat"})]})]}):null})()}),d&&(0,m.jsx)(fo,{fileNode:d,onClose:()=>e(null),onSave:a=>{console.log("Saving content for",d.name,a),e(null)}})]})}let fr=v("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]]),fs=v("folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]),ft=v("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),fu=v("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),fv=v("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);function fw({analysisResult:a}){let[b,c]=(0,n.useState)(new Set),[d,e]=(0,n.useState)(null),[f,g]=(0,n.useState)(null),h=({node:a,level:d,onNodeSelect:e,selectedNode:f})=>{let g=b.has(a.id),i=f?.id===a.id,j=a.children&&a.children.length>0,k=(a=>{if("directory"===a.type)return b.has(a.id)?fr:fs;switch(a.name.split(".").pop()?.toLowerCase()){case"js":case"jsx":case"ts":case"tsx":case"py":case"java":case"cpp":case"c":case"cs":case"php":case"rb":case"go":case"rs":case"swift":case"kt":return ff;case"png":case"jpg":case"jpeg":case"gif":case"svg":case"webp":return fg;case"json":case"xml":case"yaml":case"yml":case"toml":case"ini":return fh;default:return fi}})(a);return(0,m.jsxs)("div",{children:[(0,m.jsxs)("div",{className:o("flex items-center py-1 px-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer rounded",i&&"bg-blue-100 dark:bg-blue-900","transition-colors duration-150"),style:{paddingLeft:`${20*d+8}px`},onClick:()=>{"directory"===a.type&&j&&(a=>{let d=new Set(b);d.has(a)?d.delete(a):d.add(a),c(d)})(a.id),e(a)},children:["directory"===a.type&&j&&(0,m.jsx)("div",{className:"mr-1",children:g?(0,m.jsx)(ft,{className:"w-4 h-4 text-gray-400"}):(0,m.jsx)(fu,{className:"w-4 h-4 text-gray-400"})}),(!j||"file"===a.type)&&(0,m.jsx)("div",{className:"w-5 mr-1"}),(0,m.jsx)(k,{className:o("w-4 h-4 mr-2",a.language&&({javascript:"text-yellow-600",typescript:"text-blue-600",python:"text-green-600",java:"text-red-600",cpp:"text-purple-600",c:"text-gray-600",csharp:"text-indigo-600",php:"text-purple-500",ruby:"text-red-500",go:"text-cyan-600",rust:"text-orange-600",swift:"text-orange-500",kotlin:"text-purple-700",html:"text-orange-500",css:"text-blue-500",scss:"text-pink-500",json:"text-yellow-500",markdown:"text-gray-700"})[a.language||""]||"text-gray-500")}),(0,m.jsx)("span",{className:o("text-sm truncate",i?"text-blue-900 dark:text-blue-100 font-medium":"text-gray-700 dark:text-gray-300"),children:a.name}),a.dependencies&&a.dependencies.length>0&&(0,m.jsx)("span",{className:"ml-auto text-xs text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900 px-1.5 py-0.5 rounded",children:a.dependencies.length})]}),"directory"===a.type&&j&&g&&(0,m.jsx)("div",{children:a.children.map(a=>(0,m.jsx)(h,{node:a,level:d+1,onNodeSelect:e,selectedNode:f},a.id))})]})},i=a.graph.nodes.filter(a=>!a.path.includes("/")||1===a.path.split("/").length);return(0,m.jsxs)("div",{className:"flex h-[600px]",children:[(0,m.jsx)("div",{className:"w-1/2 border-r border-gray-200 dark:border-gray-700 overflow-y-auto",children:(0,m.jsxs)("div",{className:"p-4",children:[(0,m.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Dosya Yapısı"}),(0,m.jsx)("div",{className:"space-y-1",children:i.map(a=>(0,m.jsx)(h,{node:a,level:0,onNodeSelect:e,selectedNode:d},a.id))})]})}),(0,m.jsx)("div",{className:"w-1/2 p-4 overflow-y-auto",children:d?(0,m.jsxs)("div",{className:"space-y-4",children:[(0,m.jsxs)("div",{children:[(0,m.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:d.name}),(0,m.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400 space-y-1",children:[(0,m.jsxs)("p",{children:[(0,m.jsx)("strong",{children:"Yol:"})," ",d.path]}),(0,m.jsxs)("p",{children:[(0,m.jsx)("strong",{children:"Tip:"})," ","file"===d.type?"Dosya":"Klas\xf6r"]}),d.language&&(0,m.jsxs)("p",{children:[(0,m.jsx)("strong",{children:"Dil:"})," ",d.language]}),d.size&&(0,m.jsxs)("p",{children:[(0,m.jsx)("strong",{children:"Boyut:"})," ",(d.size/1024).toFixed(1)," KB"]})]})]}),d.dependencies&&d.dependencies.length>0&&(0,m.jsxs)("div",{children:[(0,m.jsxs)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-2",children:["Bağımlılıklar (",d.dependencies.length,")"]}),(0,m.jsx)("div",{className:"space-y-1",children:d.dependencies.map((a,b)=>(0,m.jsx)("div",{className:"text-sm text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded",children:a},b))})]}),d.content&&(0,m.jsxs)("div",{children:[(0,m.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,m.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white",children:"İ\xe7erik \xd6nizleme"}),(0,m.jsxs)("button",{onClick:()=>g(d),className:"flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300",children:[(0,m.jsx)(fv,{className:"w-4 h-4"}),(0,m.jsx)("span",{children:"D\xfczenle"})]})]}),(0,m.jsxs)("pre",{className:"text-xs bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto max-h-64 overflow-y-auto",children:[(0,m.jsx)("code",{children:d.content.slice(0,1e3)}),d.content.length>1e3&&(0,m.jsx)("div",{className:"text-gray-500 mt-2",children:"... (devamı var)"})]})]})]}):(0,m.jsxs)("div",{className:"text-center text-gray-500 dark:text-gray-400 mt-8",children:[(0,m.jsx)(fi,{className:"w-12 h-12 mx-auto mb-4 opacity-50"}),(0,m.jsx)("p",{children:"Detayları g\xf6rmek i\xe7in bir dosya veya klas\xf6r se\xe7in"})]})}),f&&(0,m.jsx)(fo,{fileNode:f,onClose:()=>g(null),onSave:a=>{console.log("Saving content for",f.name,a),g(null)}})]})}var fx=c(1261),fy=c.n(fx);let fz=v("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),fA=v("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),fB=v("git-fork",[["circle",{cx:"12",cy:"18",r:"3",key:"1mpf1b"}],["circle",{cx:"6",cy:"6",r:"3",key:"1lh9wr"}],["circle",{cx:"18",cy:"6",r:"3",key:"1h7g24"}],["path",{d:"M18 9v2c0 .6-.4 1-1 1H7c-.6 0-1-.4-1-1V9",key:"1uq4wg"}],["path",{d:"M12 12v3",key:"158kv8"}]]),fC=v("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);function fD({analysisResult:a}){let{repo:b,languages:c,stats:d}=a,e=Object.entries(c).sort(([,a],[,b])=>b-a),f=Object.values(c).reduce((a,b)=>a+b,0);return(0,m.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6",children:[(0,m.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,m.jsxs)("div",{className:"flex-1",children:[(0,m.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,m.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:b.full_name}),(0,m.jsx)("a",{href:b.html_url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300",children:(0,m.jsx)(fz,{className:"w-4 h-4"})})]}),b.description&&(0,m.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-3",children:b.description}),(0,m.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400",children:[(0,m.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,m.jsx)(fA,{className:"w-4 h-4"}),(0,m.jsx)("span",{children:"Stars"})]}),(0,m.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,m.jsx)(fB,{className:"w-4 h-4"}),(0,m.jsx)("span",{children:"Forks"})]}),(0,m.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,m.jsx)(fC,{className:"w-4 h-4"}),(0,m.jsx)("span",{children:"Watchers"})]})]})]}),(0,m.jsx)("div",{className:"flex-shrink-0 ml-4",children:(0,m.jsx)(fy(),{src:b.owner.avatar_url,alt:b.owner.login,width:48,height:48,className:"w-12 h-12 rounded-full"})})]}),(0,m.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,m.jsxs)("div",{className:"space-y-3",children:[(0,m.jsx)("h3",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"İstatistikler"}),(0,m.jsxs)("div",{className:"space-y-2",children:[(0,m.jsxs)("div",{className:"flex items-center justify-between",children:[(0,m.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,m.jsx)(fi,{className:"w-4 h-4 text-gray-400"}),(0,m.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Dosyalar"})]}),(0,m.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:d.totalFiles})]}),(0,m.jsxs)("div",{className:"flex items-center justify-between",children:[(0,m.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,m.jsx)(fs,{className:"w-4 h-4 text-gray-400"}),(0,m.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Klas\xf6rler"})]}),(0,m.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:d.totalDirectories})]}),(0,m.jsxs)("div",{className:"flex items-center justify-between",children:[(0,m.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,m.jsx)(fj,{className:"w-4 h-4 text-gray-400"}),(0,m.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Bağımlılıklar"})]}),(0,m.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:d.totalDependencies})]})]})]}),(0,m.jsxs)("div",{className:"space-y-3",children:[(0,m.jsx)("h3",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Programlama Dilleri"}),(0,m.jsx)("div",{className:"space-y-2",children:e.slice(0,5).map(([a,b])=>{let c=(b/f*100).toFixed(1);return(0,m.jsxs)("div",{className:"space-y-1",children:[(0,m.jsxs)("div",{className:"flex items-center justify-between",children:[(0,m.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:a}),(0,m.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-500",children:[c,"%"]})]}),(0,m.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5",children:(0,m.jsx)("div",{className:"bg-blue-600 h-1.5 rounded-full",style:{width:`${c}%`}})})]},a)})})]}),(0,m.jsxs)("div",{className:"space-y-3",children:[(0,m.jsx)("h3",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Repository Detayları"}),(0,m.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,m.jsxs)("div",{children:[(0,m.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Sahip: "}),(0,m.jsx)("span",{className:"text-gray-900 dark:text-white",children:b.owner.login})]}),(0,m.jsxs)("div",{children:[(0,m.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Ana Dal: "}),(0,m.jsx)("span",{className:"text-gray-900 dark:text-white",children:b.default_branch})]}),b.language&&(0,m.jsxs)("div",{children:[(0,m.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Ana Dil: "}),(0,m.jsx)("span",{className:"text-gray-900 dark:text-white",children:b.language})]})]})]})]})]})}let fE=v("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),fF=v("folder-tree",[["path",{d:"M20 10a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1h-2.5a1 1 0 0 1-.8-.4l-.9-1.2A1 1 0 0 0 15 3h-2a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1Z",key:"hod4my"}],["path",{d:"M20 21a1 1 0 0 0 1-1v-3a1 1 0 0 0-1-1h-2.9a1 1 0 0 1-.88-.55l-.42-.85a1 1 0 0 0-.92-.6H13a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1Z",key:"w4yl2u"}],["path",{d:"M3 5a2 2 0 0 0 2 2h3",key:"f2jnh7"}],["path",{d:"M3 3v13a2 2 0 0 0 2 2h3",key:"k8epm1"}]]),fG=v("network",[["rect",{x:"16",y:"16",width:"6",height:"6",rx:"1",key:"4q2zg0"}],["rect",{x:"2",y:"16",width:"6",height:"6",rx:"1",key:"8cvhb9"}],["rect",{x:"9",y:"2",width:"6",height:"6",rx:"1",key:"1egb70"}],["path",{d:"M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3",key:"1jsf9p"}],["path",{d:"M12 12V8",key:"2874zd"}]]);function fH({analysisResult:a,onReset:b}){let[c,d]=(0,n.useState)("tree");return(0,m.jsxs)("div",{className:"space-y-6",children:[(0,m.jsxs)("div",{className:"flex items-center justify-between",children:[(0,m.jsxs)(p,{variant:"outline",onClick:b,className:"flex items-center space-x-2",children:[(0,m.jsx)(fE,{className:"w-4 h-4"}),(0,m.jsx)("span",{children:"Yeni Analiz"})]}),(0,m.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,m.jsxs)(p,{variant:"tree"===c?"default":"outline",onClick:()=>d("tree"),className:"flex items-center space-x-2",children:[(0,m.jsx)(fF,{className:"w-4 h-4"}),(0,m.jsx)("span",{children:"Tree View"})]}),(0,m.jsxs)(p,{variant:"graph"===c?"default":"outline",onClick:()=>d("graph"),className:"flex items-center space-x-2",children:[(0,m.jsx)(fG,{className:"w-4 h-4"}),(0,m.jsx)("span",{children:"Node Graph"})]})]})]}),(0,m.jsx)(fD,{analysisResult:a}),(0,m.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border",children:"tree"===c?(0,m.jsx)(fw,{analysisResult:a}):(0,m.jsx)(fq,{analysisResult:a})})]})}function fI(){let[a,b]=(0,n.useState)(null),[c,d]=(0,n.useState)(!1);return(0,m.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,m.jsx)("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b",children:(0,m.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,m.jsx)("div",{className:"flex items-center justify-between",children:(0,m.jsxs)("div",{children:[(0,m.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"BaseGraph"}),(0,m.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"GitHub kod tabanı g\xf6rselleştirme ve analiz aracı"})]})})})}),(0,m.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[!a&&!c&&(0,m.jsx)("div",{className:"text-center",children:(0,m.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,m.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"GitHub Repository Analizi"}),(0,m.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-8",children:"Bir GitHub repository URL'si girin ve kod tabanınızın g\xf6rsel analizini g\xf6r\xfcn"}),(0,m.jsx)(y,{onAnalysisStart:()=>{d(!0),b(null)},onAnalysisComplete:a=>{b(a),d(!1)}})]})}),c&&(0,m.jsxs)("div",{className:"text-center py-12",children:[(0,m.jsx)("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,m.jsx)("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:"Repository analiz ediliyor..."})]}),a&&(0,m.jsx)(fH,{analysisResult:a,onReset:()=>b(null)})]})]})}},4953:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"getImgProps",{enumerable:!0,get:function(){return i}}),c(148);let d=c(1480),e=c(2756),f=["-moz-initial","fill","none","scale-down",void 0];function g(a){return void 0!==a.default}function h(a){return void 0===a?a:"number"==typeof a?Number.isFinite(a)?a:NaN:"string"==typeof a&&/^[0-9]+$/.test(a)?parseInt(a,10):NaN}function i(a,b){var c,i;let j,k,l,{src:m,sizes:n,unoptimized:o=!1,priority:p=!1,loading:q,className:r,quality:s,width:t,height:u,fill:v=!1,style:w,overrideSrc:x,onLoad:y,onLoadingComplete:z,placeholder:A="empty",blurDataURL:B,fetchPriority:C,decoding:D="async",layout:E,objectFit:F,objectPosition:G,lazyBoundary:H,lazyRoot:I,...J}=a,{imgConf:K,showAltText:L,blurComplete:M,defaultLoader:N}=b,O=K||e.imageConfigDefault;if("allSizes"in O)j=O;else{let a=[...O.deviceSizes,...O.imageSizes].sort((a,b)=>a-b),b=O.deviceSizes.sort((a,b)=>a-b),d=null==(c=O.qualities)?void 0:c.sort((a,b)=>a-b);j={...O,allSizes:a,deviceSizes:b,qualities:d}}if(void 0===N)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let P=J.loader||N;delete J.loader,delete J.srcSet;let Q="__next_img_default"in P;if(Q){if("custom"===j.loader)throw Object.defineProperty(Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let a=P;P=b=>{let{config:c,...d}=b;return a(d)}}if(E){"fill"===E&&(v=!0);let a={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[E];a&&(w={...w,...a});let b={responsive:"100vw",fill:"100vw"}[E];b&&!n&&(n=b)}let R="",S=h(t),T=h(u);if((i=m)&&"object"==typeof i&&(g(i)||void 0!==i.src)){let a=g(m)?m.default:m;if(!a.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!a.height||!a.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(k=a.blurWidth,l=a.blurHeight,B=B||a.blurDataURL,R=a.src,!v)if(S||T){if(S&&!T){let b=S/a.width;T=Math.round(a.height*b)}else if(!S&&T){let b=T/a.height;S=Math.round(a.width*b)}}else S=a.width,T=a.height}let U=!p&&("lazy"===q||void 0===q);(!(m="string"==typeof m?m:R)||m.startsWith("data:")||m.startsWith("blob:"))&&(o=!0,U=!1),j.unoptimized&&(o=!0),Q&&!j.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(o=!0);let V=h(s),W=Object.assign(v?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:F,objectPosition:G}:{},L?{}:{color:"transparent"},w),X=M||"empty"===A?null:"blur"===A?'url("data:image/svg+xml;charset=utf-8,'+(0,d.getImageBlurSvg)({widthInt:S,heightInt:T,blurWidth:k,blurHeight:l,blurDataURL:B||"",objectFit:W.objectFit})+'")':'url("'+A+'")',Y=f.includes(W.objectFit)?"fill"===W.objectFit?"100% 100%":"cover":W.objectFit,Z=X?{backgroundSize:Y,backgroundPosition:W.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:X}:{},$=function(a){let{config:b,src:c,unoptimized:d,width:e,quality:f,sizes:g,loader:h}=a;if(d)return{src:c,srcSet:void 0,sizes:void 0};let{widths:i,kind:j}=function(a,b,c){let{deviceSizes:d,allSizes:e}=a;if(c){let a=/(^|\s)(1?\d?\d)vw/g,b=[];for(let d;d=a.exec(c);)b.push(parseInt(d[2]));if(b.length){let a=.01*Math.min(...b);return{widths:e.filter(b=>b>=d[0]*a),kind:"w"}}return{widths:e,kind:"w"}}return"number"!=typeof b?{widths:d,kind:"w"}:{widths:[...new Set([b,2*b].map(a=>e.find(b=>b>=a)||e[e.length-1]))],kind:"x"}}(b,e,g),k=i.length-1;return{sizes:g||"w"!==j?g:"100vw",srcSet:i.map((a,d)=>h({config:b,src:c,quality:f,width:a})+" "+("w"===j?a:d+1)+j).join(", "),src:h({config:b,src:c,quality:f,width:i[k]})}}({config:j,src:m,unoptimized:o,width:S,quality:V,sizes:n,loader:P});return{props:{...J,loading:U?"lazy":q,fetchPriority:C,width:S,height:T,decoding:D,className:r,style:{...W,...Z},sizes:$.sizes,srcSet:$.srcSet,src:x||$.src},meta:{unoptimized:o,priority:p,placeholder:A,fill:v}}}},4959:(a,b,c)=>{"use strict";a.exports=c(4041).vendored.contexts.AmpContext},5799:(a,b,c)=>{Promise.resolve().then(c.bind(c,4951))},6422:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,6133,23)),Promise.resolve().then(c.t.bind(c,6444,23)),Promise.resolve().then(c.t.bind(c,6042,23)),Promise.resolve().then(c.t.bind(c,9477,23)),Promise.resolve().then(c.t.bind(c,9345,23)),Promise.resolve().then(c.t.bind(c,2089,23)),Promise.resolve().then(c.t.bind(c,6577,23)),Promise.resolve().then(c.t.bind(c,1307,23)),Promise.resolve().then(c.t.bind(c,4817,23))},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6533:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"Image",{enumerable:!0,get:function(){return u}});let d=c(4985),e=c(740),f=c(687),g=e._(c(3210)),h=d._(c(1215)),i=d._(c(512)),j=c(4953),k=c(2756),l=c(7903);c(148);let m=c(9148),n=d._(c(1933)),o=c(3038),p={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function q(a,b,c,d,e,f,g){let h=null==a?void 0:a.src;a&&a["data-loaded-src"]!==h&&(a["data-loaded-src"]=h,("decode"in a?a.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(a.parentElement&&a.isConnected){if("empty"!==b&&e(!0),null==c?void 0:c.current){let b=new Event("load");Object.defineProperty(b,"target",{writable:!1,value:a});let d=!1,e=!1;c.current({...b,nativeEvent:b,currentTarget:a,target:a,isDefaultPrevented:()=>d,isPropagationStopped:()=>e,persist:()=>{},preventDefault:()=>{d=!0,b.preventDefault()},stopPropagation:()=>{e=!0,b.stopPropagation()}})}(null==d?void 0:d.current)&&d.current(a)}}))}function r(a){return g.use?{fetchPriority:a}:{fetchpriority:a}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let s=(0,g.forwardRef)((a,b)=>{let{src:c,srcSet:d,sizes:e,height:h,width:i,decoding:j,className:k,style:l,fetchPriority:m,placeholder:n,loading:p,unoptimized:s,fill:t,onLoadRef:u,onLoadingCompleteRef:v,setBlurComplete:w,setShowAltText:x,sizesInput:y,onLoad:z,onError:A,...B}=a,C=(0,g.useCallback)(a=>{a&&(A&&(a.src=a.src),a.complete&&q(a,n,u,v,w,s,y))},[c,n,u,v,w,A,s,y]),D=(0,o.useMergedRef)(b,C);return(0,f.jsx)("img",{...B,...r(m),loading:p,width:i,height:h,decoding:j,"data-nimg":t?"fill":"1",className:k,style:l,sizes:e,srcSet:d,src:c,ref:D,onLoad:a=>{q(a.currentTarget,n,u,v,w,s,y)},onError:a=>{x(!0),"empty"!==n&&w(!0),A&&A(a)}})});function t(a){let{isAppRouter:b,imgAttributes:c}=a,d={as:"image",imageSrcSet:c.srcSet,imageSizes:c.sizes,crossOrigin:c.crossOrigin,referrerPolicy:c.referrerPolicy,...r(c.fetchPriority)};return b&&h.default.preload?(h.default.preload(c.src,d),null):(0,f.jsx)(i.default,{children:(0,f.jsx)("link",{rel:"preload",href:c.srcSet?void 0:c.src,...d},"__nimg-"+c.src+c.srcSet+c.sizes)})}let u=(0,g.forwardRef)((a,b)=>{let c=(0,g.useContext)(m.RouterContext),d=(0,g.useContext)(l.ImageConfigContext),e=(0,g.useMemo)(()=>{var a;let b=p||d||k.imageConfigDefault,c=[...b.deviceSizes,...b.imageSizes].sort((a,b)=>a-b),e=b.deviceSizes.sort((a,b)=>a-b),f=null==(a=b.qualities)?void 0:a.sort((a,b)=>a-b);return{...b,allSizes:c,deviceSizes:e,qualities:f}},[d]),{onLoad:h,onLoadingComplete:i}=a,o=(0,g.useRef)(h);(0,g.useEffect)(()=>{o.current=h},[h]);let q=(0,g.useRef)(i);(0,g.useEffect)(()=>{q.current=i},[i]);let[r,u]=(0,g.useState)(!1),[v,w]=(0,g.useState)(!1),{props:x,meta:y}=(0,j.getImgProps)(a,{defaultLoader:n.default,imgConf:e,blurComplete:r,showAltText:v});return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(s,{...x,unoptimized:y.unoptimized,placeholder:y.placeholder,fill:y.fill,onLoadRef:o,onLoadingCompleteRef:q,setBlurComplete:u,setShowAltText:w,sizesInput:a.sizes,ref:b}),y.priority?(0,f.jsx)(t,{isAppRouter:!c,imgAttributes:x}):null]})});("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},7498:()=>{},7755:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return f}});let d=c(3210),e=()=>{};function f(a){var b;let{headManager:c,reduceComponentsToState:f}=a;function g(){if(c&&c.mountedInstances){let b=d.Children.toArray(Array.from(c.mountedInstances).filter(Boolean));c.updateHead(f(b,a))}}return null==c||null==(b=c.mountedInstances)||b.add(a.children),g(),e(()=>{var b;return null==c||null==(b=c.mountedInstances)||b.add(a.children),()=>{var b;null==c||null==(b=c.mountedInstances)||b.delete(a.children)}}),e(()=>(c&&(c._pendingUpdate=g),()=>{c&&(c._pendingUpdate=g)})),null}},7903:(a,b,c)=>{"use strict";a.exports=c(4041).vendored.contexts.ImageConfigContext},8354:a=>{"use strict";a.exports=require("util")},8847:(a,b,c)=>{Promise.resolve().then(c.bind(c,1204))},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9148:(a,b,c)=>{"use strict";a.exports=c(4041).vendored.contexts.RouterContext},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9346:()=>{},9513:(a,b,c)=>{"use strict";a.exports=c(4041).vendored.contexts.HeadManagerContext},9733:(a,b,c)=>{"use strict";a.exports=c(907)},9760:(a,b,c)=>{"use strict";a.exports=c(3332)}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[985,400],()=>b(b.s=4224));module.exports=c})();